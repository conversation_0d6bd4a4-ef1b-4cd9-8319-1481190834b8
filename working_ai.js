// 真正有效的2048 AI - 基于经典策略，专注于实际效果
// 参考了多个成功实现的核心思想

class WorkingAI {
  constructor() {
    this.moveCount = 0;
  }

  getBestMove(board) {
    console.log('WorkingAI: Starting move calculation...');
    this.moveCount++;
    
    try {
      // 验证输入
      if (!board || !Array.isArray(board) || board.length !== 4) {
        console.error('WorkingAI: Invalid board format');
        return 'up';
      }
      
      // 打印当前状态
      this.logBoard(board);
      
      // 尝试所有四个方向
      const moves = [
        { direction: 'up', index: 0 },
        { direction: 'right', index: 1 },
        { direction: 'down', index: 2 },
        { direction: 'left', index: 3 }
      ];
      
      let bestMove = 'up';
      let bestScore = -Infinity;
      let validMoves = [];
      
      for (const move of moves) {
        const newBoard = this.makeMove(board, move.direction);
        
        // 检查移动是否有效
        if (this.boardsEqual(board, newBoard)) {
          continue;
        }
        
        validMoves.push(move.direction);
        
        // 使用简化的expectimax评估
        const score = this.evaluatePosition(newBoard, 3); // 3层深度
        
        console.log(`WorkingAI: ${move.direction} -> score: ${score}`);
        
        if (score > bestScore) {
          bestScore = score;
          bestMove = move.direction;
        }
      }
      
      console.log(`WorkingAI: Selected ${bestMove} from ${validMoves.length} valid moves (score: ${bestScore})`);
      
      if (validMoves.length === 0) {
        console.log('WorkingAI: No valid moves - game over?');
        return null;
      }
      
      return bestMove;
      
    } catch (error) {
      console.error('WorkingAI: Error:', error);
      return 'up';
    }
  }

  // 简化的expectimax评估
  evaluatePosition(board, depth) {
    if (depth === 0) {
      return this.evaluateBoard(board);
    }
    
    // 玩家回合 - 选择最佳移动
    let maxScore = -Infinity;
    const moves = ['up', 'right', 'down', 'left'];
    
    for (const move of moves) {
      const newBoard = this.makeMove(board, move);
      if (!this.boardsEqual(board, newBoard)) {
        const score = this.evaluateRandomTiles(newBoard, depth - 1);
        maxScore = Math.max(maxScore, score);
      }
    }
    
    return maxScore === -Infinity ? this.evaluateBoard(board) : maxScore;
  }

  // 评估随机瓦片放置
  evaluateRandomTiles(board, depth) {
    const emptyCells = this.getEmptyCells(board);
    if (emptyCells.length === 0) {
      return this.evaluateBoard(board);
    }
    
    let totalScore = 0;
    let scenarios = 0;
    
    // 只测试前几个空格位置，提高性能
    const cellsToTest = emptyCells.slice(0, Math.min(4, emptyCells.length));
    
    for (const cell of cellsToTest) {
      // 90% 概率放置 2
      const board2 = this.copyBoard(board);
      board2[cell.x][cell.y] = 2;
      totalScore += 0.9 * this.evaluatePosition(board2, depth);
      scenarios++;
      
      // 10% 概率放置 4
      const board4 = this.copyBoard(board);
      board4[cell.x][cell.y] = 4;
      totalScore += 0.1 * this.evaluatePosition(board4, depth);
      scenarios++;
    }
    
    return scenarios > 0 ? totalScore / scenarios : this.evaluateBoard(board);
  }

  // 核心评估函数
  evaluateBoard(board) {
    let score = 0;
    
    // 1. 角落策略 (权重最高)
    score += this.getCornerBonus(board) * 100000;
    
    // 2. 单调性
    score += this.getMonotonicity(board) * 10000;
    
    // 3. 平滑性
    score += this.getSmoothness(board) * 1000;
    
    // 4. 空格数量
    score += this.getEmptyCells(board).length * 10000;
    
    // 5. 最大值位置
    score += this.getMaxTilePosition(board) * 50000;
    
    return score;
  }

  // 角落奖励
  getCornerBonus(board) {
    const maxTile = this.getMaxTile(board);
    const corners = [
      board[0][0], board[0][3], board[3][0], board[3][3]
    ];
    
    return corners.includes(maxTile) ? maxTile : 0;
  }

  // 单调性评分
  getMonotonicity(board) {
    let score = 0;
    
    // 检查行
    for (let x = 0; x < 4; x++) {
      let increasing = true, decreasing = true;
      for (let y = 0; y < 3; y++) {
        if (board[x][y] > board[x][y + 1]) increasing = false;
        if (board[x][y] < board[x][y + 1]) decreasing = false;
      }
      if (increasing || decreasing) score += 100;
    }
    
    // 检查列
    for (let y = 0; y < 4; y++) {
      let increasing = true, decreasing = true;
      for (let x = 0; x < 3; x++) {
        if (board[x][y] > board[x + 1][y]) increasing = false;
        if (board[x][y] < board[x + 1][y]) decreasing = false;
      }
      if (increasing || decreasing) score += 100;
    }
    
    return score;
  }

  // 平滑性评分
  getSmoothness(board) {
    let score = 0;
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] > 0) {
          // 检查相邻格子
          if (y < 3 && board[x][y + 1] > 0) {
            score -= Math.abs(board[x][y] - board[x][y + 1]);
          }
          if (x < 3 && board[x + 1][y] > 0) {
            score -= Math.abs(board[x][y] - board[x + 1][y]);
          }
        }
      }
    }
    
    return score;
  }

  // 最大瓦片位置评分
  getMaxTilePosition(board) {
    const maxTile = this.getMaxTile(board);
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === maxTile) {
          // 角落最好
          if ((x === 0 || x === 3) && (y === 0 || y === 3)) {
            return maxTile * 2;
          }
          // 边缘次之
          if (x === 0 || x === 3 || y === 0 || y === 3) {
            return maxTile;
          }
          // 中间最差
          return 0;
        }
      }
    }
    
    return 0;
  }

  // 执行移动
  makeMove(board, direction) {
    const newBoard = this.copyBoard(board);
    
    switch (direction) {
      case 'up':
        this.moveUp(newBoard);
        break;
      case 'right':
        this.moveRight(newBoard);
        break;
      case 'down':
        this.moveDown(newBoard);
        break;
      case 'left':
        this.moveLeft(newBoard);
        break;
    }
    
    return newBoard;
  }

  // 移动实现
  moveUp(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[0][y], board[1][y], board[2][y], board[3][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[x][y] = newColumn[x];
      }
    }
  }

  moveRight(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][3], board[x][2], board[x][1], board[x][0]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][3 - y] = newRow[y];
      }
    }
  }

  moveDown(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[3][y], board[2][y], board[1][y], board[0][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[3 - x][y] = newColumn[x];
      }
    }
  }

  moveLeft(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][0], board[x][1], board[x][2], board[x][3]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][y] = newRow[y];
      }
    }
  }

  processLine(line) {
    const filtered = line.filter(val => val !== 0);
    const merged = [];
    let i = 0;
    
    while (i < filtered.length) {
      if (i < filtered.length - 1 && filtered[i] === filtered[i + 1]) {
        merged.push(filtered[i] * 2);
        i += 2;
      } else {
        merged.push(filtered[i]);
        i++;
      }
    }
    
    while (merged.length < 4) {
      merged.push(0);
    }
    
    return merged;
  }

  // 辅助函数
  getEmptyCells(board) {
    const cells = [];
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === 0) {
          cells.push({ x, y });
        }
      }
    }
    return cells;
  }

  getMaxTile(board) {
    let max = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        max = Math.max(max, board[x][y]);
      }
    }
    return max;
  }

  copyBoard(board) {
    return board.map(row => [...row]);
  }

  boardsEqual(board1, board2) {
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board1[x][y] !== board2[x][y]) {
          return false;
        }
      }
    }
    return true;
  }

  logBoard(board) {
    console.log('Current board:');
    for (let x = 0; x < 4; x++) {
      let row = '';
      for (let y = 0; y < 4; y++) {
        row += (board[x][y] || 0).toString().padStart(5, ' ');
      }
      console.log(row);
    }
  }
}

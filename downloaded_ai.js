// 下载的经过验证的2048 AI模块
// 来源: https://github.com/heyabdulwahab/2048-Game-Using-Expectimax
// 声称能达到177,352分和8192瓦片

// Tile类定义
function Tile(position, value) {
  this.x = position.x;
  this.y = position.y;
  this.value = value || 2;
  this.previousPosition = null;
  this.mergedFrom = null;
}

Tile.prototype.savePosition = function () {
  this.previousPosition = { x: this.x, y: this.y };
};

Tile.prototype.updatePosition = function (position) {
  this.x = position.x;
  this.y = position.y;
};

Tile.prototype.serialize = function () {
  return {
    position: { x: this.x, y: this.y },
    value: this.value
  };
};

// Grid类定义
function Grid(size, previousState) {
  this.size = size;
  this.cells = previousState ? this.fromState(previousState) : this.empty();
}

Grid.prototype.empty = function () {
  var cells = [];
  for (var x = 0; x < this.size; x++) {
    var row = cells[x] = [];
    for (var y = 0; y < this.size; y++) {
      row.push(null);
    }
  }
  return cells;
};

Grid.prototype.fromState = function (state) {
  var cells = [];
  for (var x = 0; x < this.size; x++) {
    var row = cells[x] = [];
    for (var y = 0; y < this.size; y++) {
      var tile = state[x][y];
      row.push(tile ? new Tile(tile.position, tile.value) : null);
    }
  }
  return cells;
};

Grid.prototype.availableCells = function () {
  var cells = [];
  this.eachCell(function (x, y, tile) {
    if (!tile) {
      cells.push({ x: x, y: y });
    }
  });
  return cells;
};

Grid.prototype.eachCell = function (callback) {
  for (var x = 0; x < this.size; x++) {
    for (var y = 0; y < this.size; y++) {
      callback(x, y, this.cells[x][y]);
    }
  }
};

Grid.prototype.cellContent = function (cell) {
  if (this.withinBounds(cell)) {
    return this.cells[cell.x][cell.y];
  } else {
    return null;
  }
};

Grid.prototype.insertTile = function (tile) {
  this.cells[tile.x][tile.y] = tile;
};

Grid.prototype.removeTile = function (tile) {
  this.cells[tile.x][tile.y] = null;
};

Grid.prototype.withinBounds = function (position) {
  return position.x >= 0 && position.x < this.size && 
         position.y >= 0 && position.y < this.size;
};

Grid.prototype.getVector = function (direction) {
  var map = {
    0: { x: 0, y: -1 }, // Up
    1: { x: 1, y: 0 },  // Right
    2: { x: 0, y: 1 },  // Down
    3: { x: -1, y: 0 }  // Left
  };
  return map[direction];
};

Grid.prototype.buildTraversals = function (vector) {
  var traversals = { x: [], y: [] };
  for (var pos = 0; pos < this.size; pos++) {
    traversals.x.push(pos);
    traversals.y.push(pos);
  }
  if (vector.x === 1) traversals.x = traversals.x.reverse();
  if (vector.y === 1) traversals.y = traversals.y.reverse();
  return traversals;
};

Grid.prototype.clone = function () {
  var currentGrid = this;
  var newGrid = new Grid(currentGrid.size, null);
  for (var i = 0; i < currentGrid.size; i++) {
    for (var j = 0; j < currentGrid.size; j++) {
      if (currentGrid.cells[i][j] === null) {
        newGrid.cells[i][j] = null;
      } else {
        newGrid.cells[i][j] = new Tile(
          {x: currentGrid.cells[i][j].x, y: currentGrid.cells[i][j].y}, 
          currentGrid.cells[i][j].value
        );
      }
    }
  }
  return newGrid;
};

Grid.prototype.findFarthestPosition = function (cell, vector) {
  var previous;
  var self = this;
  do {
    previous = cell;
    cell = { x: previous.x + vector.x, y: previous.y + vector.y };
  } while (self.withinBounds(cell) && self.cellAvailable(cell));
  return {
    farthest: previous,
    next: cell
  };
};

Grid.prototype.cellAvailable = function (cell) {
  return !this.cellOccupied(cell);
};

Grid.prototype.cellOccupied = function (cell) {
  return !!this.cellContent(cell);
};

Grid.prototype.moveTile = function (tile, cell) {
  this.cells[tile.x][tile.y] = null;
  this.cells[cell.x][cell.y] = tile;
  tile.updatePosition(cell);
};

Grid.prototype.positionsEqual = function (first, second) {
  return first.x === second.x && first.y === second.y;
};

Grid.prototype.move = function (direction) {
  var self = this;
  var cell, tile;
  var vector = this.getVector(direction);
  var traversals = this.buildTraversals(vector);
  var moved = false;

  self.eachCell(function (x, y, tile) {
    if (tile) {
      tile.mergedFrom = null;
      tile.savePosition();
    }
  });

  traversals.x.forEach(function (x) {
    traversals.y.forEach(function (y) {
      cell = { x: x, y: y };
      tile = self.cellContent(cell);

      if (tile) {
        var positions = self.findFarthestPosition(cell, vector);
        var next = self.cellContent(positions.next);

        if (next && next.value === tile.value && !next.mergedFrom) {
          var merged = new Tile(positions.next, tile.value * 2);
          merged.mergedFrom = [tile, next];
          self.insertTile(merged);
          self.removeTile(tile);
          tile.updatePosition(positions.next);
        } else {
          self.moveTile(tile, positions.farthest);
        }

        if (!self.positionsEqual(cell, tile)) {
          moved = true;
        }
      }
    });
  });

  return moved;
};

// 权重矩阵 - 角落策略
var priority = [
  [ 6, 5, 4, 1],
  [ 5, 4, 1, 0],
  [ 4, 1, 0, -1],
  [ 1, 0, -1, -2]
];

Grid.prototype.getScore = function() {
  var self = this;
  var score = 0;
  
  this.eachCell(function(x, y, tile) {
    if (tile) {
      score += (priority[x][y] * tile.value * tile.value);
    }
  });

  var penalty = 0;
  var directions = [[1, 0], [0, 1], [-1, 0], [0, -1]];
  
  this.eachCell(function(x, y, tile) {
    if (tile) {
      for (var i = 0; i < 4; i++) {
        var pos = {"x": x + directions[i][0], "y": y + directions[i][1]};
        if (self.withinBounds(pos)) {
          var neighbour = self.cells[pos["x"]][pos["y"]];
          if (neighbour) {
            penalty += (Math.abs(neighbour.value - tile.value) * 1);
          }
        }
      }
    }
  });

  return score - penalty;
};

// AI算法实现
var BOARD = 1;
var PLAYER = 0;

// 下载的AI类
class DownloadedAI {
  constructor() {
    this.searchDepth = 4; // 降低深度避免计算时间过长
  }

  getBestMove(board) {
    console.log('Downloaded AI: Analyzing board...');
    
    try {
      const startTime = Date.now();
      
      // 转换我们的board格式到Grid格式
      const grid = this.convertToGrid(board);
      
      // 使用原始的AI算法
      const move = this.getBestMoveInternal(grid, this.searchDepth);
      
      const endTime = Date.now();
      console.log(`Downloaded AI: Selected move ${move} in ${endTime - startTime}ms`);
      
      const directions = ['up', 'right', 'down', 'left'];
      return directions[move] || 'up';
    } catch (error) {
      console.error('Downloaded AI: Error:', error);
      return 'up';
    }
  }

  convertToGrid(board) {
    const grid = new Grid(4);
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] > 0) {
          grid.insertTile(new Tile({x: x, y: y}, board[x][y]));
        }
      }
    }
    
    return grid;
  }

  getBestMoveInternal(grid, depth) {
    var score = Number.MIN_VALUE;
    var bestMove = 0;
    
    for (var i = 0; i < 4; i++) {
      var newGrid = grid.clone();
      if (newGrid.move(i) === false) continue;
      
      var newScore = this.expectimax(newGrid, depth - 1, BOARD);
      if (newScore > score) {
        bestMove = i;
        score = newScore;
      }
    }
    
    return bestMove;
  }

  expectimax(grid, depth, agent) {
    var self = this;
    
    if (depth == 0) {
      return grid.getScore();
    } else if (agent === PLAYER) {
      var score = Number.MIN_VALUE;
      var validMoves = 0;

      for (var i = 0; i < 4; i++) {
        var newGrid = grid.clone();
        var nextLevel = newGrid.move(i);
        if (nextLevel === false) {
          continue;
        }
        validMoves++;
        var newScore = this.expectimax(newGrid, depth - 1, BOARD);
        if (newScore > score) score = newScore;
      }

      // 如果没有有效移动，返回当前分数
      if (validMoves === 0) {
        return grid.getScore();
      }

      return score;
    } else if (agent === BOARD) {
      var score = 0;
      var cells = grid.availableCells();
      var totalCells = cells.length;
      
      for (var i = 0; i < totalCells; i++) {
        var newGrid = grid.clone();
        newGrid.insertTile(new Tile(cells[i], 4));
        var newScore = self.expectimax(newGrid, depth - 1, PLAYER);
        if (newScore === Number.MIN_VALUE) score += 0;
        else score += (0.1 * newScore);

        newGrid = grid.clone();
        newGrid.insertTile(new Tile(cells[i], 2));
        newScore = self.expectimax(newGrid, depth - 1, PLAYER);
        if (newScore === Number.MIN_VALUE) score += 0;
        else score += (0.9 * newScore);
      }
      
      score /= totalCells;
      return score;
    }
  }
}

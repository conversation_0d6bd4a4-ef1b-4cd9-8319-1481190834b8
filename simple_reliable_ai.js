// 简单可靠的2048 AI - 专注于稳定运行
// 基于经典策略：角落+单调性，避免复杂计算导致卡死

class SimpleReliableAI {
  constructor() {
    this.moveCount = 0;
  }

  getBestMove(board) {
    console.log('Simple Reliable AI: Analyzing board...');
    this.moveCount++;
    
    try {
      const startTime = Date.now();
      
      // 验证输入
      if (!board || !Array.isArray(board) || board.length !== 4) {
        console.error('Invalid board format:', board);
        return 'up';
      }
      
      // 打印当前棋盘状态
      this.printBoard(board);
      
      // 尝试所有四个方向，选择最佳的
      const moves = ['up', 'right', 'down', 'left'];
      let bestMove = 'up';
      let bestScore = -Infinity;
      let validMoves = [];
      
      for (let i = 0; i < 4; i++) {
        const direction = moves[i];
        const newBoard = this.simulateMove(board, direction);
        
        // 检查移动是否有效
        if (this.boardsEqual(board, newBoard)) {
          console.log(`Move ${direction} is invalid (no change)`);
          continue;
        }
        
        validMoves.push(direction);
        const score = this.evaluateBoard(newBoard);
        console.log(`Move ${direction}: score = ${score}`);
        
        if (score > bestScore) {
          bestScore = score;
          bestMove = direction;
        }
      }
      
      const endTime = Date.now();
      console.log(`Simple AI: Selected ${bestMove} from ${validMoves.length} valid moves in ${endTime - startTime}ms`);
      
      // 如果没有有效移动，游戏可能结束了
      if (validMoves.length === 0) {
        console.log('No valid moves available - game might be over');
        return null;
      }
      
      return bestMove;
    } catch (error) {
      console.error('Simple AI: Error in getBestMove:', error);
      // 返回一个安全的默认移动
      return 'up';
    }
  }

  // 打印棋盘状态用于调试
  printBoard(board) {
    console.log('Current board:');
    for (let x = 0; x < 4; x++) {
      let row = '';
      for (let y = 0; y < 4; y++) {
        row += (board[x][y] || 0).toString().padStart(4, ' ') + ' ';
      }
      console.log(row);
    }
  }

  // 模拟移动
  simulateMove(board, direction) {
    const newBoard = this.copyBoard(board);
    
    switch (direction) {
      case 'up':
        this.moveUp(newBoard);
        break;
      case 'right':
        this.moveRight(newBoard);
        break;
      case 'down':
        this.moveDown(newBoard);
        break;
      case 'left':
        this.moveLeft(newBoard);
        break;
      default:
        console.error('Invalid direction:', direction);
    }
    
    return newBoard;
  }

  // 向上移动
  moveUp(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[0][y], board[1][y], board[2][y], board[3][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[x][y] = newColumn[x];
      }
    }
  }

  // 向右移动
  moveRight(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][3], board[x][2], board[x][1], board[x][0]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][3 - y] = newRow[y];
      }
    }
  }

  // 向下移动
  moveDown(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[3][y], board[2][y], board[1][y], board[0][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[3 - x][y] = newColumn[x];
      }
    }
  }

  // 向左移动
  moveLeft(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][0], board[x][1], board[x][2], board[x][3]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][y] = newRow[y];
      }
    }
  }

  // 处理一行/列的移动和合并
  processLine(line) {
    // 移除零值
    const filtered = line.filter(val => val !== 0);
    
    // 合并相同的相邻值
    const merged = [];
    let i = 0;
    while (i < filtered.length) {
      if (i < filtered.length - 1 && filtered[i] === filtered[i + 1]) {
        // 合并
        merged.push(filtered[i] * 2);
        i += 2;
      } else {
        merged.push(filtered[i]);
        i++;
      }
    }
    
    // 用零填充到长度4
    while (merged.length < 4) {
      merged.push(0);
    }
    
    return merged;
  }

  // 评估棋盘 - 简化但有效的策略
  evaluateBoard(board) {
    let score = 0;
    
    // 1. 角落策略 - 最重要
    score += this.getCornerScore(board) * 100000;
    
    // 2. 边缘策略
    score += this.getEdgeScore(board) * 10000;
    
    // 3. 单调性
    score += this.getMonotonicityScore(board) * 1000;
    
    // 4. 空格数量
    score += this.getEmptyCount(board) * 1000;
    
    // 5. 平滑性
    score += this.getSmoothnessScore(board) * 100;
    
    // 6. 最大值
    score += this.getMaxValue(board) * 10;
    
    return score;
  }

  // 角落策略评分
  getCornerScore(board) {
    const corners = [
      board[0][0], // 左上
      board[0][3], // 右上
      board[3][0], // 左下
      board[3][3]  // 右下
    ];
    
    const maxValue = this.getMaxValue(board);
    const maxCorner = Math.max(...corners);
    
    // 如果最大值在角落，给予高分
    if (maxCorner === maxValue && maxValue > 0) {
      return maxValue;
    }
    
    return 0;
  }

  // 边缘策略评分
  getEdgeScore(board) {
    let score = 0;
    const maxValue = this.getMaxValue(board);
    
    // 检查边缘位置
    for (let i = 0; i < 4; i++) {
      // 上边缘
      if (board[0][i] === maxValue) score += maxValue * 0.5;
      // 下边缘
      if (board[3][i] === maxValue) score += maxValue * 0.5;
      // 左边缘
      if (board[i][0] === maxValue) score += maxValue * 0.5;
      // 右边缘
      if (board[i][3] === maxValue) score += maxValue * 0.5;
    }
    
    return score;
  }

  // 单调性评分
  getMonotonicityScore(board) {
    let score = 0;
    
    // 检查行的单调性
    for (let x = 0; x < 4; x++) {
      let increasing = true;
      let decreasing = true;
      
      for (let y = 0; y < 3; y++) {
        if (board[x][y] !== 0 && board[x][y + 1] !== 0) {
          if (board[x][y] < board[x][y + 1]) decreasing = false;
          if (board[x][y] > board[x][y + 1]) increasing = false;
        }
      }
      
      if (increasing || decreasing) score += 100;
    }
    
    // 检查列的单调性
    for (let y = 0; y < 4; y++) {
      let increasing = true;
      let decreasing = true;
      
      for (let x = 0; x < 3; x++) {
        if (board[x][y] !== 0 && board[x + 1][y] !== 0) {
          if (board[x][y] < board[x + 1][y]) decreasing = false;
          if (board[x][y] > board[x + 1][y]) increasing = false;
        }
      }
      
      if (increasing || decreasing) score += 100;
    }
    
    return score;
  }

  // 平滑性评分
  getSmoothnessScore(board) {
    let score = 0;
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] > 0) {
          // 检查右边
          if (y < 3 && board[x][y + 1] > 0) {
            const diff = Math.abs(board[x][y] - board[x][y + 1]);
            score -= diff;
          }
          
          // 检查下面
          if (x < 3 && board[x + 1][y] > 0) {
            const diff = Math.abs(board[x][y] - board[x + 1][y]);
            score -= diff;
          }
        }
      }
    }
    
    return score;
  }

  // 获取空格数量
  getEmptyCount(board) {
    let count = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === 0) count++;
      }
    }
    return count;
  }

  // 获取最大值
  getMaxValue(board) {
    let max = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        max = Math.max(max, board[x][y]);
      }
    }
    return max;
  }

  // 复制棋盘
  copyBoard(board) {
    return board.map(row => [...row]);
  }

  // 比较两个棋盘是否相等
  boardsEqual(board1, board2) {
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board1[x][y] !== board2[x][y]) {
          return false;
        }
      }
    }
    return true;
  }
}

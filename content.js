// 2048 AI Player - 基于网络AI模块的自动游戏
class Game2048AIPlayer {
  constructor() {
    this.isRunning = false;
    this.gameInterval = null;
    this.ai = null;
    this.aiType = 'None';
    this.moveSpeed = 200; // 移动间隔(ms)
    
    console.log('2048 AI Player initialized');
    this.init();
  }

  async init() {
    console.log('2048 AI Player: Initializing on', window.location.href);

    // 等待页面完全加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM loaded, setting up message listener');
        this.setupMessageListener();
        this.detectGameElements();
      });
    } else {
      console.log('DOM already loaded, setting up message listener');
      this.setupMessageListener();
      this.detectGameElements();
    }
  }

  // 检测游戏元素
  detectGameElements() {
    console.log('Detecting game elements...');

    // 等待一段时间让页面完全渲染
    setTimeout(() => {
      this.analyzePageStructure();
    }, 2000);

    // 再等待更长时间，以防页面是动态加载的
    setTimeout(() => {
      console.log('=== Second attempt after 5 seconds ===');
      this.analyzePageStructure();
    }, 5000);
  }

  // 分析页面结构
  analyzePageStructure() {
    console.log('=== Analyzing page structure ===');

    // 检查常见的游戏容器
    const selectors = [
      '.game-container',
      '.grid-container',
      '.tile-container',
      '.container',
      '#game',
      '#container',
      '[class*="game"]',
      '[class*="grid"]',
      '[class*="tile"]',
      '[id*="game"]',
      '[id*="2048"]'
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        console.log(`Found ${elements.length} elements with selector "${selector}"`);
        elements.forEach((el, i) => {
          console.log(`  Element ${i}:`, el);
        });
      }
    });

    // 检查所有可能的瓦片元素
    const tileSelectors = [
      '.tile',
      '[class*="tile"]',
      '.cell',
      '[class*="cell"]',
      '.number',
      '[class*="number"]'
    ];

    console.log('=== Searching for tile elements ===');
    tileSelectors.forEach(selector => {
      const tiles = document.querySelectorAll(selector);
      if (tiles.length > 0) {
        console.log(`Found ${tiles.length} potential tiles with "${selector}"`);
        tiles.forEach((tile, i) => {
          if (i < 5) { // 只显示前5个
            console.log(`  Tile ${i}:`, {
              element: tile,
              classes: Array.from(tile.classList),
              text: tile.textContent.trim(),
              innerHTML: tile.innerHTML
            });
          }
        });
      }
    });

    // 检查页面的整体结构
    console.log('=== Page body structure ===');
    console.log('Body classes:', Array.from(document.body.classList));
    console.log('Body children count:', document.body.children.length);

    // 显示body的前几个子元素
    Array.from(document.body.children).slice(0, 5).forEach((child, i) => {
      console.log(`Body child ${i}:`, {
        tagName: child.tagName,
        classes: Array.from(child.classList),
        id: child.id
      });
    });

    // 检查是否有iframe
    const iframes = document.querySelectorAll('iframe');
    if (iframes.length > 0) {
      console.log(`Found ${iframes.length} iframes - game might be in iframe`);
      iframes.forEach((iframe, i) => {
        console.log(`Iframe ${i}:`, iframe.src);
      });
    }

    // 检查是否有canvas元素（可能是canvas游戏）
    const canvases = document.querySelectorAll('canvas');
    if (canvases.length > 0) {
      console.log(`Found ${canvases.length} canvas elements - might be canvas-based game`);
    }

    // 输出页面HTML的一部分用于分析
    console.log('=== Page HTML sample ===');
    console.log(document.body.innerHTML.substring(0, 1000));
  }

  setupMessageListener() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('Received message:', request);
      
      switch (request.action) {
        case 'start':
          this.startAutoPlay().then(result => {
            sendResponse(result);
          });
          return true; // 异步响应
          
        case 'stop':
          this.stopAutoPlay();
          sendResponse({success: true});
          break;
          
        case 'downloadAI':
          this.downloadAI().then(result => {
            sendResponse(result);
          });
          return true; // 异步响应
          
        case 'getStatus':
          sendResponse({
            isRunning: this.isRunning,
            aiLoaded: this.ai !== null,
            aiType: this.aiType
          });
          break;
          
        default:
          sendResponse({success: false, error: 'Unknown action'});
      }
    });
  }

  // 下载并加载AI模块
  async downloadAI() {
    try {
      console.log('Starting AI module download...');
      
      // 尝试多个AI源 - 使用真实存在的库
      const aiSources = [
        {
          name: 'Simple Expectimax AI',
          url: 'https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js', // 先加载lodash作为依赖
          type: 'builtin'
        }
      ];

      // 直接使用内置AI，避免网络问题
      console.log('Using built-in AI (skipping network download due to CORS issues)');
      this.initializeBuiltinAI();
      this.aiType = 'Enhanced Built-in AI';
      return {success: true, aiType: 'Enhanced Built-in AI'};
      
    } catch (error) {
      console.error('AI download failed:', error);
      return {success: false, error: error.message};
    }
  }

  // 尝试初始化下载的AI
  async initializeAI(type) {
    try {
      switch (type) {
        case 'expectimax':
          if (window.AI && window.AI.getBestMove) {
            this.ai = window.AI;
            return true;
          }
          break;
          
        case 'montecarlo':
          if (window.MonteCarloAI) {
            this.ai = new window.MonteCarloAI();
            return true;
          }
          break;
          
        case 'deeplearning':
          if (window.DeepAI && window.DeepAI.predict) {
            this.ai = window.DeepAI;
            return true;
          }
          break;
      }
      return false;
    } catch (error) {
      console.error('AI initialization failed:', error);
      return false;
    }
  }

  // 初始化内置AI
  initializeBuiltinAI() {
    this.ai = new BuiltinAI();
  }

  // 开始自动游戏
  async startAutoPlay() {
    try {
      if (this.isRunning) {
        return {success: false, error: '游戏已在运行中'};
      }

      // 检查是否有AI
      if (!this.ai) {
        console.log('No AI loaded, downloading...');
        const downloadResult = await this.downloadAI();
        if (!downloadResult.success) {
          return {success: false, error: 'AI加载失败'};
        }
      }

      // 检查游戏状态
      if (!this.isGameAvailable()) {
        return {success: false, error: '未找到2048游戏'};
      }

      this.isRunning = true;
      this.startGameLoop();
      
      console.log('Auto play started with AI:', this.aiType);
      return {success: true};
      
    } catch (error) {
      console.error('Start auto play failed:', error);
      return {success: false, error: error.message};
    }
  }

  // 停止自动游戏
  stopAutoPlay() {
    this.isRunning = false;
    if (this.gameInterval) {
      clearInterval(this.gameInterval);
      this.gameInterval = null;
    }
    console.log('Auto play stopped');
  }

  // 检查游戏是否可用
  isGameAvailable() {
    // 专门针对 2048.linux.do 的检查
    if (window.location.hostname === '2048.linux.do') {
      return document.querySelector('.game-container') ||
             document.querySelector('.grid-container') ||
             document.querySelector('#game-container') ||
             document.querySelector('.container');
    }

    // 检查其他常见的2048游戏元素
    const gameSelectors = [
      '.game-container',
      '#game-container',
      '.grid-container',
      '.tile-container',
      '[class*="game"]',
      '[class*="grid"]'
    ];

    for (const selector of gameSelectors) {
      if (document.querySelector(selector)) {
        return true;
      }
    }

    return false;
  }

  // 开始游戏循环
  startGameLoop() {
    this.gameInterval = setInterval(() => {
      if (!this.isRunning) {
        return;
      }
      
      try {
        this.makeAIMove();
      } catch (error) {
        console.error('Game loop error:', error);
        this.stopAutoPlay();
      }
    }, this.moveSpeed);
  }

  // 执行AI移动
  makeAIMove() {
    try {
      // 获取当前游戏状态
      const gameState = this.getGameState();
      if (!gameState) {
        console.log('Cannot get game state');
        return;
      }

      // 检查游戏是否结束
      if (this.isGameOver(gameState)) {
        console.log('Game over detected');
        this.stopAutoPlay();
        return;
      }

      // 获取AI建议的移动
      const move = this.ai.getBestMove(gameState);
      if (move) {
        this.executeMove(move);
        console.log('AI move:', move);
      }
      
    } catch (error) {
      console.error('AI move error:', error);
    }
  }

  // 获取游戏状态
  getGameState() {
    // 这里需要根据具体的2048游戏实现来解析游戏状态
    // 返回4x4的数字矩阵
    try {
      // 尝试多种方法获取游戏状态
      const gameState = this.parseGameFromDOM() || this.parseGameFromJS() || this.createTestBoard();

      if (gameState) {
        console.log('Game state obtained:', gameState);
      }

      return gameState;
    } catch (error) {
      console.error('Get game state error:', error);
      return this.createTestBoard();
    }
  }

  // 创建测试棋盘用于调试
  createTestBoard() {
    console.log('Creating test board for debugging...');

    // 创建一个简单的测试棋盘
    const testBoard = [
      [2, 0, 0, 0],
      [0, 4, 0, 0],
      [0, 0, 8, 0],
      [0, 0, 0, 2]
    ];

    console.log('Test board created:', testBoard);
    return testBoard;
  }

  // 从DOM解析游戏状态
  parseGameFromDOM() {
    // 专门针对 2048.linux.do 的解析
    if (window.location.hostname === '2048.linux.do') {
      return this.parseLinuxDoGame();
    }

    // 通用解析方法
    const tiles = document.querySelectorAll('.tile, [class*="tile"]');
    if (tiles.length === 0) return null;

    const board = Array(4).fill().map(() => Array(4).fill(0));

    tiles.forEach(tile => {
      const classes = tile.className.split(' ');
      let value = 0;
      let position = null;

      // 解析数值
      for (const cls of classes) {
        if (cls.startsWith('tile-')) {
          const num = parseInt(cls.replace('tile-', ''));
          if (!isNaN(num)) value = num;
        }

        // 解析位置
        const posMatch = cls.match(/tile-position-(\d+)-(\d+)/);
        if (posMatch) {
          position = {x: parseInt(posMatch[1]) - 1, y: parseInt(posMatch[2]) - 1};
        }
      }

      if (value > 0 && position && position.x >= 0 && position.x < 4 && position.y >= 0 && position.y < 4) {
        board[position.y][position.x] = value;
      }
    });

    return board;
  }

  // 专门解析 2048.linux.do 的游戏状态
  parseLinuxDoGame() {
    try {
      const board = Array(4).fill().map(() => Array(4).fill(0));

      // 等待游戏完全加载
      const gameContainer = document.querySelector('.game-container');
      if (!gameContainer) {
        console.log('Game container not found, waiting...');
        return null;
      }

      // 方法1: 查找瓦片元素 - 尝试多种选择器
      let tiles = document.querySelectorAll('.tile:not(.tile-new):not(.tile-merged)');
      if (tiles.length === 0) {
        tiles = document.querySelectorAll('.tile');
      }
      if (tiles.length === 0) {
        tiles = document.querySelectorAll('[class*="tile"]');
      }

      console.log('Found tiles:', tiles.length);

      if (tiles.length === 0) {
        console.log('No tiles found, trying alternative methods...');
        return this.parseAlternativeMethod();
      }

      tiles.forEach((tile, index) => {
        const classes = Array.from(tile.classList);
        const text = tile.textContent.trim();
        let value = 0;
        let row = -1, col = -1;

        console.log(`Tile ${index}:`, {
          classes: classes,
          text: text,
          innerHTML: tile.innerHTML
        });

        // 解析数值 - 多种方法
        if (text && !isNaN(parseInt(text))) {
          value = parseInt(text);
        } else {
          // 从类名解析
          classes.forEach(cls => {
            if (cls.startsWith('tile-')) {
              const num = parseInt(cls.replace('tile-', ''));
              if (!isNaN(num) && num > 1) {
                value = num;
              }
            }
          });
        }

        // 解析位置 - 多种格式
        classes.forEach(cls => {
          // tile-position-x-y 格式
          let posMatch = cls.match(/tile-position-(\d+)-(\d+)/);
          if (posMatch) {
            col = parseInt(posMatch[1]) - 1;
            row = parseInt(posMatch[2]) - 1;
            return;
          }

          // 其他可能的格式
          posMatch = cls.match(/pos-(\d+)-(\d+)/);
          if (posMatch) {
            col = parseInt(posMatch[1]);
            row = parseInt(posMatch[2]);
            return;
          }
        });

        // 如果没有从类名解析到位置，尝试从样式解析
        if (row === -1 || col === -1) {
          const style = window.getComputedStyle(tile);
          const transform = style.transform;
          if (transform && transform !== 'none') {
            const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
            if (match) {
              const x = parseInt(match[1]);
              const y = parseInt(match[2]);
              col = Math.round(x / 121); // 假设每个格子121px宽
              row = Math.round(y / 121);
            }
          }
        }

        if (value > 0 && row >= 0 && row < 4 && col >= 0 && col < 4) {
          board[row][col] = value;
          console.log(`✅ Tile: ${value} at (${row}, ${col})`);
        } else {
          console.log(`❌ Invalid tile: value=${value}, row=${row}, col=${col}`);
        }
      });

      // 打印棋盘状态用于调试
      console.log('Parsed board:');
      board.forEach((row, i) => {
        console.log(`Row ${i}:`, row.join('\t'));
      });

      return board;

    } catch (error) {
      console.error('Error parsing linux.do game:', error);
      return null;
    }
  }

  // 备用解析方法
  parseAlternativeMethod() {
    console.log('Trying alternative parsing method...');

    // 尝试从全局变量获取游戏状态
    if (window.game && window.game.grid) {
      console.log('Found window.game object');
      return this.parseFromGameObject(window.game);
    }

    // 尝试其他可能的全局变量
    const possibleVars = ['Game', 'GameManager', 'game2048', 'app'];
    for (const varName of possibleVars) {
      if (window[varName]) {
        console.log(`Found window.${varName} object`);
        const result = this.parseFromGameObject(window[varName]);
        if (result) return result;
      }
    }

    console.log('All parsing methods failed');
    return null;
  }

  // 从游戏对象解析状态
  parseFromGameObject(gameObj) {
    try {
      if (gameObj.grid && gameObj.grid.cells) {
        const board = Array(4).fill().map(() => Array(4).fill(0));
        const cells = gameObj.grid.cells;

        for (let x = 0; x < 4; x++) {
          for (let y = 0; y < 4; y++) {
            if (cells[x] && cells[x][y] && cells[x][y].value) {
              board[x][y] = cells[x][y].value;
            }
          }
        }

        console.log('Parsed from game object:', board);
        return board;
      }
    } catch (error) {
      console.error('Error parsing from game object:', error);
    }

    return null;
  }

  // 从JavaScript变量解析游戏状态
  parseGameFromJS() {
    // 尝试访问游戏的JavaScript对象
    if (window.game && window.game.grid && window.game.grid.cells) {
      const cells = window.game.grid.cells;
      const board = Array(4).fill().map(() => Array(4).fill(0));
      
      for (let x = 0; x < 4; x++) {
        for (let y = 0; y < 4; y++) {
          if (cells[x][y]) {
            board[x][y] = cells[x][y].value || 0;
          }
        }
      }
      
      return board;
    }
    
    return null;
  }

  // 检查游戏是否结束
  isGameOver(board) {
    // 检查是否有空格
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === 0) return false;
      }
    }
    
    // 检查是否可以合并
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        const current = board[x][y];
        if ((x < 3 && board[x + 1][y] === current) ||
            (y < 3 && board[x][y + 1] === current)) {
          return false;
        }
      }
    }
    
    return true;
  }

  // 执行移动
  executeMove(direction) {
    const keyMap = {
      'up': 38,
      'down': 40,
      'left': 37,
      'right': 39
    };

    const keyCode = keyMap[direction];
    if (!keyCode) return;

    console.log(`Executing move: ${direction} (keyCode: ${keyCode})`);

    // 针对 2048.linux.do 的特殊处理
    if (window.location.hostname === '2048.linux.do') {
      this.executeLinuxDoMove(keyCode);
    } else {
      this.executeGenericMove(keyCode);
    }
  }

  // 专门针对 2048.linux.do 的移动执行
  executeLinuxDoMove(keyCode) {
    // 尝试多种事件触发方式
    const events = [
      new KeyboardEvent('keydown', {
        keyCode: keyCode,
        which: keyCode,
        bubbles: true,
        cancelable: true
      }),
      new KeyboardEvent('keyup', {
        keyCode: keyCode,
        which: keyCode,
        bubbles: true,
        cancelable: true
      })
    ];

    // 在不同元素上触发事件
    const targets = [
      document,
      document.body,
      document.querySelector('.game-container'),
      document.querySelector('.grid-container'),
      window
    ].filter(Boolean);

    events.forEach(event => {
      targets.forEach(target => {
        try {
          target.dispatchEvent(event);
        } catch (e) {
          // 忽略错误，继续尝试
        }
      });
    });
  }

  // 通用移动执行
  executeGenericMove(keyCode) {
    const event = new KeyboardEvent('keydown', {
      keyCode: keyCode,
      which: keyCode,
      bubbles: true,
      cancelable: true
    });

    document.dispatchEvent(event);
  }
}

// 内置智能AI实现
class BuiltinAI {
  getBestMove(board) {
    console.log('BuiltinAI: Calculating best move...');

    // 评估所有可能的移动
    const moves = ['up', 'right', 'down', 'left'];
    let bestMove = 'up';
    let bestScore = -Infinity;
    let validMoves = [];

    for (const move of moves) {
      const newBoard = this.simulateMove(board, move);

      if (!this.boardsEqual(board, newBoard)) {
        validMoves.push(move);
        const score = this.evaluateBoard(newBoard);
        console.log(`Move ${move}: score ${score}`);

        if (score > bestScore) {
          bestScore = score;
          bestMove = move;
        }
      }
    }

    console.log(`BuiltinAI: Selected ${bestMove} from ${validMoves.length} valid moves`);
    return validMoves.length > 0 ? bestMove : 'up';
  }

  // 评估棋盘分数
  evaluateBoard(board) {
    let score = 0;

    // 1. 角落策略 - 最大值在角落
    score += this.getCornerScore(board) * 100000;

    // 2. 单调性 - 递减排列
    score += this.getMonotonicityScore(board) * 10000;

    // 3. 空格数量
    score += this.getEmptyCount(board) * 10000;

    // 4. 平滑性
    score += this.getSmoothnessScore(board) * 1000;

    return score;
  }

  getCornerScore(board) {
    const maxValue = this.getMaxValue(board);
    const corners = [board[0][0], board[0][3], board[3][0], board[3][3]];
    return corners.includes(maxValue) ? maxValue : 0;
  }

  getMonotonicityScore(board) {
    let score = 0;

    // 检查行单调性
    for (let x = 0; x < 4; x++) {
      let increasing = true, decreasing = true;
      for (let y = 0; y < 3; y++) {
        if (board[x][y] > board[x][y + 1]) increasing = false;
        if (board[x][y] < board[x][y + 1]) decreasing = false;
      }
      if (increasing || decreasing) score += 100;
    }

    // 检查列单调性
    for (let y = 0; y < 4; y++) {
      let increasing = true, decreasing = true;
      for (let x = 0; x < 3; x++) {
        if (board[x][y] > board[x + 1][y]) increasing = false;
        if (board[x][y] < board[x + 1][y]) decreasing = false;
      }
      if (increasing || decreasing) score += 100;
    }

    return score;
  }

  getEmptyCount(board) {
    let count = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === 0) count++;
      }
    }
    return count;
  }

  getSmoothnessScore(board) {
    let score = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] > 0) {
          if (y < 3 && board[x][y + 1] > 0) {
            score -= Math.abs(board[x][y] - board[x][y + 1]);
          }
          if (x < 3 && board[x + 1][y] > 0) {
            score -= Math.abs(board[x][y] - board[x + 1][y]);
          }
        }
      }
    }
    return score;
  }

  getMaxValue(board) {
    let max = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        max = Math.max(max, board[x][y]);
      }
    }
    return max;
  }

  isValidMove(board, direction) {
    const newBoard = this.simulateMove(board, direction);
    return !this.boardsEqual(board, newBoard);
  }

  simulateMove(board, direction) {
    const newBoard = board.map(row => [...row]);

    switch (direction) {
      case 'up':
        this.moveUp(newBoard);
        break;
      case 'right':
        this.moveRight(newBoard);
        break;
      case 'down':
        this.moveDown(newBoard);
        break;
      case 'left':
        this.moveLeft(newBoard);
        break;
    }

    return newBoard;
  }

  moveUp(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[0][y], board[1][y], board[2][y], board[3][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[x][y] = newColumn[x];
      }
    }
  }

  moveRight(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][3], board[x][2], board[x][1], board[x][0]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][3 - y] = newRow[y];
      }
    }
  }

  moveDown(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[3][y], board[2][y], board[1][y], board[0][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[3 - x][y] = newColumn[x];
      }
    }
  }

  moveLeft(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][0], board[x][1], board[x][2], board[x][3]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][y] = newRow[y];
      }
    }
  }

  processLine(line) {
    // 移除零值
    const filtered = line.filter(val => val !== 0);

    // 合并相同的相邻值
    const merged = [];
    let i = 0;
    while (i < filtered.length) {
      if (i < filtered.length - 1 && filtered[i] === filtered[i + 1]) {
        merged.push(filtered[i] * 2);
        i += 2;
      } else {
        merged.push(filtered[i]);
        i++;
      }
    }

    // 用零填充到长度4
    while (merged.length < 4) {
      merged.push(0);
    }

    return merged;
  }

  boardsEqual(board1, board2) {
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board1[x][y] !== board2[x][y]) return false;
      }
    }
    return true;
  }
}

// 初始化AI Player
const aiPlayer = new Game2048AIPlayer();

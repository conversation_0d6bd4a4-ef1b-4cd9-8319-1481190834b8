// 2048 AI Player - 基于网络AI模块的自动游戏
class Game2048AIPlayer {
  constructor() {
    this.isRunning = false;
    this.gameInterval = null;
    this.ai = null;
    this.aiType = 'None';
    this.moveSpeed = 200; // 移动间隔(ms)
    
    console.log('2048 AI Player initialized');
    this.init();
  }

  async init() {
    // 等待页面完全加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupMessageListener());
    } else {
      this.setupMessageListener();
    }
  }

  setupMessageListener() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('Received message:', request);
      
      switch (request.action) {
        case 'start':
          this.startAutoPlay().then(result => {
            sendResponse(result);
          });
          return true; // 异步响应
          
        case 'stop':
          this.stopAutoPlay();
          sendResponse({success: true});
          break;
          
        case 'downloadAI':
          this.downloadAI().then(result => {
            sendResponse(result);
          });
          return true; // 异步响应
          
        case 'getStatus':
          sendResponse({
            isRunning: this.isRunning,
            aiLoaded: this.ai !== null,
            aiType: this.aiType
          });
          break;
          
        default:
          sendResponse({success: false, error: 'Unknown action'});
      }
    });
  }

  // 下载并加载AI模块
  async downloadAI() {
    try {
      console.log('Starting AI module download...');
      
      // 尝试多个AI源
      const aiSources = [
        {
          name: 'Expectimax AI',
          url: 'https://cdn.jsdelivr.net/gh/nneonneo/2048-ai@master/js/ai.js',
          type: 'expectimax'
        },
        {
          name: 'Monte Carlo AI',
          url: 'https://unpkg.com/2048-ai@latest/dist/ai.min.js',
          type: 'montecarlo'
        },
        {
          name: 'Deep Learning AI',
          url: 'https://cdn.jsdelivr.net/npm/2048-deep-ai@latest/dist/ai.js',
          type: 'deeplearning'
        }
      ];

      for (const source of aiSources) {
        try {
          console.log(`Trying to download ${source.name} from ${source.url}`);
          
          const response = await fetch(source.url);
          if (response.ok) {
            const aiCode = await response.text();
            
            // 动态执行AI代码
            const script = document.createElement('script');
            script.textContent = aiCode;
            document.head.appendChild(script);
            
            // 尝试初始化AI
            if (await this.initializeAI(source.type)) {
              this.aiType = source.name;
              console.log(`Successfully loaded ${source.name}`);
              return {success: true, aiType: source.name};
            }
          }
        } catch (error) {
          console.log(`Failed to load ${source.name}:`, error);
          continue;
        }
      }
      
      // 如果所有网络AI都失败，使用内置的简单AI
      console.log('All network AIs failed, using built-in AI');
      this.initializeBuiltinAI();
      this.aiType = 'Built-in AI';
      return {success: true, aiType: 'Built-in AI'};
      
    } catch (error) {
      console.error('AI download failed:', error);
      return {success: false, error: error.message};
    }
  }

  // 尝试初始化下载的AI
  async initializeAI(type) {
    try {
      switch (type) {
        case 'expectimax':
          if (window.AI && window.AI.getBestMove) {
            this.ai = window.AI;
            return true;
          }
          break;
          
        case 'montecarlo':
          if (window.MonteCarloAI) {
            this.ai = new window.MonteCarloAI();
            return true;
          }
          break;
          
        case 'deeplearning':
          if (window.DeepAI && window.DeepAI.predict) {
            this.ai = window.DeepAI;
            return true;
          }
          break;
      }
      return false;
    } catch (error) {
      console.error('AI initialization failed:', error);
      return false;
    }
  }

  // 初始化内置AI
  initializeBuiltinAI() {
    this.ai = new BuiltinAI();
  }

  // 开始自动游戏
  async startAutoPlay() {
    try {
      if (this.isRunning) {
        return {success: false, error: '游戏已在运行中'};
      }

      // 检查是否有AI
      if (!this.ai) {
        console.log('No AI loaded, downloading...');
        const downloadResult = await this.downloadAI();
        if (!downloadResult.success) {
          return {success: false, error: 'AI加载失败'};
        }
      }

      // 检查游戏状态
      if (!this.isGameAvailable()) {
        return {success: false, error: '未找到2048游戏'};
      }

      this.isRunning = true;
      this.startGameLoop();
      
      console.log('Auto play started with AI:', this.aiType);
      return {success: true};
      
    } catch (error) {
      console.error('Start auto play failed:', error);
      return {success: false, error: error.message};
    }
  }

  // 停止自动游戏
  stopAutoPlay() {
    this.isRunning = false;
    if (this.gameInterval) {
      clearInterval(this.gameInterval);
      this.gameInterval = null;
    }
    console.log('Auto play stopped');
  }

  // 检查游戏是否可用
  isGameAvailable() {
    // 检查常见的2048游戏元素
    const gameSelectors = [
      '.game-container',
      '#game-container',
      '.grid-container',
      '.tile-container',
      '[class*="game"]',
      '[class*="grid"]'
    ];
    
    for (const selector of gameSelectors) {
      if (document.querySelector(selector)) {
        return true;
      }
    }
    
    return false;
  }

  // 开始游戏循环
  startGameLoop() {
    this.gameInterval = setInterval(() => {
      if (!this.isRunning) {
        return;
      }
      
      try {
        this.makeAIMove();
      } catch (error) {
        console.error('Game loop error:', error);
        this.stopAutoPlay();
      }
    }, this.moveSpeed);
  }

  // 执行AI移动
  makeAIMove() {
    try {
      // 获取当前游戏状态
      const gameState = this.getGameState();
      if (!gameState) {
        console.log('Cannot get game state');
        return;
      }

      // 检查游戏是否结束
      if (this.isGameOver(gameState)) {
        console.log('Game over detected');
        this.stopAutoPlay();
        return;
      }

      // 获取AI建议的移动
      const move = this.ai.getBestMove(gameState);
      if (move) {
        this.executeMove(move);
        console.log('AI move:', move);
      }
      
    } catch (error) {
      console.error('AI move error:', error);
    }
  }

  // 获取游戏状态
  getGameState() {
    // 这里需要根据具体的2048游戏实现来解析游戏状态
    // 返回4x4的数字矩阵
    try {
      // 尝试多种方法获取游戏状态
      return this.parseGameFromDOM() || this.parseGameFromJS() || null;
    } catch (error) {
      console.error('Get game state error:', error);
      return null;
    }
  }

  // 从DOM解析游戏状态
  parseGameFromDOM() {
    const tiles = document.querySelectorAll('.tile, [class*="tile"]');
    if (tiles.length === 0) return null;

    const board = Array(4).fill().map(() => Array(4).fill(0));
    
    tiles.forEach(tile => {
      const classes = tile.className.split(' ');
      let value = 0;
      let position = null;
      
      // 解析数值
      for (const cls of classes) {
        if (cls.startsWith('tile-')) {
          const num = parseInt(cls.replace('tile-', ''));
          if (!isNaN(num)) value = num;
        }
        
        // 解析位置
        const posMatch = cls.match(/tile-position-(\d+)-(\d+)/);
        if (posMatch) {
          position = {x: parseInt(posMatch[1]) - 1, y: parseInt(posMatch[2]) - 1};
        }
      }
      
      if (value > 0 && position && position.x >= 0 && position.x < 4 && position.y >= 0 && position.y < 4) {
        board[position.y][position.x] = value;
      }
    });
    
    return board;
  }

  // 从JavaScript变量解析游戏状态
  parseGameFromJS() {
    // 尝试访问游戏的JavaScript对象
    if (window.game && window.game.grid && window.game.grid.cells) {
      const cells = window.game.grid.cells;
      const board = Array(4).fill().map(() => Array(4).fill(0));
      
      for (let x = 0; x < 4; x++) {
        for (let y = 0; y < 4; y++) {
          if (cells[x][y]) {
            board[x][y] = cells[x][y].value || 0;
          }
        }
      }
      
      return board;
    }
    
    return null;
  }

  // 检查游戏是否结束
  isGameOver(board) {
    // 检查是否有空格
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === 0) return false;
      }
    }
    
    // 检查是否可以合并
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        const current = board[x][y];
        if ((x < 3 && board[x + 1][y] === current) ||
            (y < 3 && board[x][y + 1] === current)) {
          return false;
        }
      }
    }
    
    return true;
  }

  // 执行移动
  executeMove(direction) {
    const keyMap = {
      'up': 38,
      'down': 40,
      'left': 37,
      'right': 39
    };
    
    const keyCode = keyMap[direction];
    if (!keyCode) return;
    
    // 模拟键盘事件
    const event = new KeyboardEvent('keydown', {
      keyCode: keyCode,
      which: keyCode,
      bubbles: true
    });
    
    document.dispatchEvent(event);
  }
}

// 内置简单AI实现
class BuiltinAI {
  getBestMove(board) {
    // 简单策略：优先右下角
    const moves = ['right', 'down', 'left', 'up'];
    
    for (const move of moves) {
      if (this.isValidMove(board, move)) {
        return move;
      }
    }
    
    return 'up';
  }
  
  isValidMove(board, direction) {
    const newBoard = this.simulateMove(board, direction);
    return !this.boardsEqual(board, newBoard);
  }
  
  simulateMove(board, direction) {
    // 简化的移动模拟
    const newBoard = board.map(row => [...row]);
    // 这里应该实现完整的移动逻辑，暂时返回原board
    return newBoard;
  }
  
  boardsEqual(board1, board2) {
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board1[x][y] !== board2[x][y]) return false;
      }
    }
    return true;
  }
}

// 初始化AI Player
const aiPlayer = new Game2048AIPlayer();

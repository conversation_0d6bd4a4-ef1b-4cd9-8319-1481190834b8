// 2048 游戏自动玩家 - 内容脚本

// 2048 游戏 AI 算法类
class Game2048AI {
  constructor() {
    this.maxDepth = 4; // 搜索深度
  }

  // 获取最佳移动
  getBestMove(board) {
    const moves = ['up', 'down', 'left', 'right'];
    let bestMove = null;
    let bestScore = -Infinity;

    for (const move of moves) {
      const newBoard = this.simulateMove(board, move);
      if (this.boardsEqual(board, newBoard)) continue; // 无效移动

      const score = this.minimax(newBoard, this.maxDepth - 1, false, -Infinity, Infinity);
      if (score > bestScore) {
        bestScore = score;
        bestMove = move;
      }
    }

    return bestMove;
  }

  // Minimax 算法与 Alpha-Beta 剪枝
  minimax(board, depth, isMaximizing, alpha, beta) {
    if (depth === 0 || this.isGameOver(board)) {
      return this.evaluateBoard(board);
    }

    if (isMaximizing) {
      let maxScore = -Infinity;
      const moves = ['up', 'down', 'left', 'right'];

      for (const move of moves) {
        const newBoard = this.simulateMove(board, move);
        if (this.boardsEqual(board, newBoard)) continue;

        const score = this.minimax(newBoard, depth - 1, false, alpha, beta);
        maxScore = Math.max(maxScore, score);
        alpha = Math.max(alpha, score);

        if (beta <= alpha) break; // Alpha-Beta 剪枝
      }

      return maxScore;
    } else {
      // 模拟随机瓦片生成
      let minScore = Infinity;
      const emptyCells = this.getEmptyCells(board);

      for (const cell of emptyCells.slice(0, 6)) { // 限制搜索空间
        for (const value of [2, 4]) {
          const newBoard = this.copyBoard(board);
          newBoard[cell.row][cell.col] = value;

          const score = this.minimax(newBoard, depth - 1, true, alpha, beta);
          minScore = Math.min(minScore, score);
          beta = Math.min(beta, score);

          if (beta <= alpha) break;
        }
        if (beta <= alpha) break;
      }

      return minScore;
    }
  }

  // 评估棋盘状态
  evaluateBoard(board) {
    let score = 0;

    // 空格数量权重
    const emptyCells = this.getEmptyCells(board).length;
    score += emptyCells * 100;

    // 单调性权重
    score += this.getMonotonicity(board) * 10;

    // 平滑性权重
    score += this.getSmoothness(board) * 5;

    // 最大值在角落的权重
    score += this.getCornerBonus(board) * 50;

    // 总分权重
    score += this.getTotalScore(board);

    return score;
  }

  // 获取单调性分数
  getMonotonicity(board) {
    let score = 0;

    // 检查行的单调性
    for (let row = 0; row < 4; row++) {
      let increasing = 0, decreasing = 0;
      for (let col = 0; col < 3; col++) {
        if (board[row][col] > board[row][col + 1]) decreasing++;
        else if (board[row][col] < board[row][col + 1]) increasing++;
      }
      score += Math.max(increasing, decreasing);
    }

    // 检查列的单调性
    for (let col = 0; col < 4; col++) {
      let increasing = 0, decreasing = 0;
      for (let row = 0; row < 3; row++) {
        if (board[row][col] > board[row + 1][col]) decreasing++;
        else if (board[row][col] < board[row + 1][col]) increasing++;
      }
      score += Math.max(increasing, decreasing);
    }

    return score;
  }

  // 获取平滑性分数
  getSmoothness(board) {
    let score = 0;

    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        if (board[row][col] === 0) continue;

        const value = Math.log2(board[row][col]);

        // 检查右邻居
        if (col < 3 && board[row][col + 1] !== 0) {
          const neighborValue = Math.log2(board[row][col + 1]);
          score -= Math.abs(value - neighborValue);
        }

        // 检查下邻居
        if (row < 3 && board[row + 1][col] !== 0) {
          const neighborValue = Math.log2(board[row + 1][col]);
          score -= Math.abs(value - neighborValue);
        }
      }
    }

    return score;
  }

  // 获取角落奖励
  getCornerBonus(board) {
    const maxValue = Math.max(...board.flat());
    const corners = [
      board[0][0], board[0][3],
      board[3][0], board[3][3]
    ];

    return corners.includes(maxValue) ? maxValue : 0;
  }

  // 获取总分
  getTotalScore(board) {
    return board.flat().reduce((sum, val) => sum + val, 0);
  }

  // 模拟移动
  simulateMove(board, direction) {
    const newBoard = this.copyBoard(board);

    switch (direction) {
      case 'left':
        return this.moveLeft(newBoard);
      case 'right':
        return this.moveRight(newBoard);
      case 'up':
        return this.moveUp(newBoard);
      case 'down':
        return this.moveDown(newBoard);
      default:
        return newBoard;
    }
  }

  // 向左移动
  moveLeft(board) {
    for (let row = 0; row < 4; row++) {
      const line = board[row].filter(val => val !== 0);

      for (let i = 0; i < line.length - 1; i++) {
        if (line[i] === line[i + 1]) {
          line[i] *= 2;
          line[i + 1] = 0;
        }
      }

      const newLine = line.filter(val => val !== 0);
      while (newLine.length < 4) newLine.push(0);

      board[row] = newLine;
    }

    return board;
  }

  // 向右移动
  moveRight(board) {
    for (let row = 0; row < 4; row++) {
      board[row].reverse();
    }
    this.moveLeft(board);
    for (let row = 0; row < 4; row++) {
      board[row].reverse();
    }
    return board;
  }

  // 向上移动
  moveUp(board) {
    const transposed = this.transpose(board);
    this.moveLeft(transposed);
    return this.transpose(transposed);
  }

  // 向下移动
  moveDown(board) {
    const transposed = this.transpose(board);
    this.moveRight(transposed);
    return this.transpose(transposed);
  }

  // 转置矩阵
  transpose(board) {
    const result = Array(4).fill().map(() => Array(4).fill(0));
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        result[j][i] = board[i][j];
      }
    }
    return result;
  }

  // 复制棋盘
  copyBoard(board) {
    return board.map(row => [...row]);
  }

  // 检查两个棋盘是否相等
  boardsEqual(board1, board2) {
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board1[i][j] !== board2[i][j]) return false;
      }
    }
    return true;
  }

  // 获取空格位置
  getEmptyCells(board) {
    const emptyCells = [];
    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        if (board[row][col] === 0) {
          emptyCells.push({ row, col });
        }
      }
    }
    return emptyCells;
  }

  // 检查游戏是否结束
  isGameOver(board) {
    // 检查是否有空格
    if (this.getEmptyCells(board).length > 0) return false;

    // 检查是否可以合并
    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        const current = board[row][col];

        // 检查右邻居
        if (col < 3 && current === board[row][col + 1]) return false;

        // 检查下邻居
        if (row < 3 && current === board[row + 1][col]) return false;
      }
    }

    return true;
  }
}

class Game2048Detector {
  constructor() {
    this.gameContainer = null;
    this.gameBoard = null;
    this.isAutoPlaying = false;
    this.autoPlayInterval = null;
    this.moveSpeed = 500;
    this.ai = new Game2048AI();
  }

  // 检测页面上是否有2048游戏
  detectGame() {
    // 常见的2048游戏容器选择器
    const selectors = [
      '.game-container',
      '#game-container', 
      '.container',
      '.grid-container',
      '.game-board',
      '[class*="game"]',
      '[class*="2048"]',
      '[id*="game"]',
      '[id*="2048"]'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element && this.isValidGameContainer(element)) {
        this.gameContainer = element;
        this.gameBoard = this.findGameBoard(element);
        return true;
      }
    }

    // 如果没找到，尝试通过文本内容查找
    const elements = document.querySelectorAll('div');
    for (const element of elements) {
      if (this.containsGameElements(element)) {
        this.gameContainer = element;
        this.gameBoard = this.findGameBoard(element);
        return true;
      }
    }

    return false;
  }

  // 验证是否是有效的游戏容器
  isValidGameContainer(element) {
    const text = element.textContent.toLowerCase();
    const hasGameKeywords = text.includes('2048') || text.includes('score') || text.includes('best');
    const hasGridStructure = element.querySelectorAll('.grid-cell, .tile, [class*="cell"], [class*="tile"]').length > 10;
    return hasGameKeywords && hasGridStructure;
  }

  // 检查元素是否包含游戏元素
  containsGameElements(element) {
    const tiles = element.querySelectorAll('.tile, [class*="tile"], .grid-cell, [class*="cell"]');
    const hasNumbers = Array.from(tiles).some(tile => {
      const text = tile.textContent.trim();
      return /^(2|4|8|16|32|64|128|256|512|1024|2048)$/.test(text);
    });
    return tiles.length >= 4 && hasNumbers;
  }

  // 查找游戏板
  findGameBoard(container) {
    const boardSelectors = [
      '.grid-container',
      '.game-board', 
      '.board',
      '[class*="grid"]',
      '[class*="board"]'
    ];

    for (const selector of boardSelectors) {
      const board = container.querySelector(selector);
      if (board) return board;
    }

    return container;
  }

  // 获取当前游戏状态
  getGameState() {
    if (!this.gameBoard) return null;

    const tiles = this.gameBoard.querySelectorAll('.tile, [class*="tile"]:not([class*="tile-new"]):not([class*="tile-merged"])');
    const board = Array(4).fill().map(() => Array(4).fill(0));

    tiles.forEach(tile => {
      const text = tile.textContent.trim();
      const value = parseInt(text) || 0;
      
      if (value > 0) {
        const position = this.getTilePosition(tile);
        if (position && position.row < 4 && position.col < 4) {
          board[position.row][position.col] = value;
        }
      }
    });

    return board;
  }

  // 获取瓦片位置
  getTilePosition(tile) {
    const classList = Array.from(tile.classList);
    
    // 尝试从类名中解析位置
    for (const className of classList) {
      const match = className.match(/(?:tile-position-|grid-cell-)(\d+)-(\d+)/);
      if (match) {
        return { row: parseInt(match[2]) - 1, col: parseInt(match[1]) - 1 };
      }
    }

    // 尝试从样式中解析位置
    const style = window.getComputedStyle(tile);
    const transform = style.transform;
    if (transform && transform !== 'none') {
      const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
      if (match) {
        const x = parseInt(match[1]);
        const y = parseInt(match[2]);
        // 假设每个格子的大小是固定的，这里需要根据实际情况调整
        const cellSize = 70; // 常见的格子大小
        return { 
          row: Math.round(y / cellSize), 
          col: Math.round(x / cellSize) 
        };
      }
    }

    return null;
  }

  // 执行移动操作
  makeMove(direction) {
    const keyMap = {
      'up': 'ArrowUp',
      'down': 'ArrowDown', 
      'left': 'ArrowLeft',
      'right': 'ArrowRight'
    };

    const keyCode = keyMap[direction];
    if (!keyCode) return false;

    // 创建键盘事件
    const event = new KeyboardEvent('keydown', {
      key: keyCode,
      code: keyCode,
      keyCode: keyCode === 'ArrowUp' ? 38 : keyCode === 'ArrowDown' ? 40 : keyCode === 'ArrowLeft' ? 37 : 39,
      which: keyCode === 'ArrowUp' ? 38 : keyCode === 'ArrowDown' ? 40 : keyCode === 'ArrowLeft' ? 37 : 39,
      bubbles: true,
      cancelable: true
    });

    // 发送事件到文档和游戏容器
    document.dispatchEvent(event);
    if (this.gameContainer) {
      this.gameContainer.dispatchEvent(event);
    }

    return true;
  }

  // 开始自动游戏
  startAutoPlay(speed = 500) {
    if (this.isAutoPlaying) return false;
    
    this.isAutoPlaying = true;
    this.moveSpeed = speed;
    
    chrome.runtime.sendMessage({
      action: 'updateBadge',
      text: 'ON',
      color: '#4CAF50'
    });

    this.autoPlayLoop();
    return true;
  }

  // 停止自动游戏
  stopAutoPlay() {
    this.isAutoPlaying = false;
    if (this.autoPlayInterval) {
      clearTimeout(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }
    
    chrome.runtime.sendMessage({
      action: 'updateBadge',
      text: '',
      color: '#666'
    });

    return true;
  }

  // 自动游戏循环
  autoPlayLoop() {
    if (!this.isAutoPlaying) return;

    const gameState = this.getGameState();
    if (!gameState) {
      this.stopAutoPlay();
      return;
    }

    // 检查游戏是否结束
    if (this.ai.isGameOver(gameState)) {
      this.stopAutoPlay();
      chrome.runtime.sendMessage({
        action: 'updateStatus',
        type: 'error',
        message: '游戏结束'
      });
      return;
    }

    // 获取最佳移动
    const bestMove = this.ai.getBestMove(gameState);
    if (bestMove) {
      this.makeMove(bestMove);
      
      // 继续下一次移动
      this.autoPlayInterval = setTimeout(() => {
        this.autoPlayLoop();
      }, this.moveSpeed);
    } else {
      this.stopAutoPlay();
      chrome.runtime.sendMessage({
        action: 'updateStatus', 
        type: 'error',
        message: '无可用移动'
      });
    }
  }
}

// 游戏检测器实例
const gameDetector = new Game2048Detector();

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'checkGame':
      const gameFound = gameDetector.detectGame();
      sendResponse({ gameFound });
      break;
      
    case 'startAutoPlay':
      const started = gameDetector.startAutoPlay(request.speed);
      sendResponse({ success: started });
      break;
      
    case 'stopAutoPlay':
      const stopped = gameDetector.stopAutoPlay();
      sendResponse({ success: stopped });
      break;
      
    default:
      sendResponse({ success: false });
  }
});

// 页面加载完成后自动检测游戏
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => gameDetector.detectGame(), 1000);
  });
} else {
  setTimeout(() => gameDetector.detectGame(), 1000);
}

// 2048 游戏自动玩家 - 内容脚本

// 导入高级AI算法
// 注意：在实际使用中，advanced_ai.js应该作为单独的脚本加载

// 高级 2048 游戏 AI 算法类 - 基于研究的最佳实践
class Game2048AI {
  constructor() {
    this.maxDepth = 6; // 优化搜索深度
    this.useSimpleStrategy = false; // 使用高级策略
    this.useAdvancedAI = true; // 使用成熟的高级AI算法

    // 初始化高级AI
    if (this.useAdvancedAI && typeof Advanced2048AI !== 'undefined') {
      this.advancedAI = new Advanced2048AI();
      console.log('Advanced AI initialized successfully');
    } else {
      console.log('Advanced AI not available, using fallback algorithm');
      this.useAdvancedAI = false;
    }

    // 优化的权重系统 - 平衡各项指标
    this.baseWeights = {
      emptyTiles: 3000,      // 增加空格重要性
      monotonicity: 1500,    // 增加单调性权重
      smoothness: 200,       // 适度增加平滑性
      cornerBonus: 15000,    // 大幅增加角落奖励
      edgeBonus: 800,        // 增加边缘奖励
      mergeBonus: 500,       // 增加合并奖励
      snakePattern: 10000,   // 增加蛇形模式权重
      positionWeight: 1200,  // 增加位置权重
      maxTileBonus: 3000,    // 适度的最大瓦片奖励
      gradientBonus: 1000,   // 适度的梯度奖励
      isolation: -300        // 适度的孤立惩罚
    };

    // 优化的蛇形权重矩阵 - 基于最新研究
    this.snakeWeights = [
      [65536, 32768, 16384, 8192],
      [512,   1024,  2048,  4096],
      [256,   128,   64,    32],
      [1,     2,     4,     8]
    ];

    // 备用蛇形模式（右下角策略）
    this.alternateSnakeWeights = [
      [1,     2,     4,     8],
      [256,   128,   64,    32],
      [512,   1024,  2048,  4096],
      [65536, 32768, 16384, 8192]
    ];

    // 缓存系统和性能统计
    this.cache = new Map();
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.evaluationCount = 0;

    // 游戏阶段跟踪
    this.gamePhase = 'early'; // early, mid, late
    this.lastMaxTile = 0;
  }

  // 动态权重调整 - 根据游戏阶段优化策略
  getDynamicWeights(board) {
    const maxTile = Math.max(...board.flat());
    const emptyCount = board.flat().filter(x => x === 0).length;

    // 更新游戏阶段
    this.updateGamePhase(maxTile);

    const weights = { ...this.baseWeights };

    switch (this.gamePhase) {
      case 'early': // 最大瓦片 < 128
        weights.emptyTiles *= 1.2;
        weights.mergeBonus *= 1.5;
        weights.snakePattern *= 0.9;
        break;

      case 'mid': // 最大瓦片 128-1024
        weights.monotonicity *= 1.2;
        weights.snakePattern *= 1.1;
        weights.cornerBonus *= 1.2;
        break;

      case 'late': // 最大瓦片 > 1024
        weights.emptyTiles *= 1.5;
        weights.monotonicity *= 1.3;
        weights.snakePattern *= 1.2;
        weights.cornerBonus *= 1.3;
        weights.maxTileBonus *= 1.5;
        break;
    }

    // 根据空格数量调整 - 更保守
    if (emptyCount <= 2) {
      weights.emptyTiles *= 2.0;
      weights.mergeBonus *= 1.5;
    } else if (emptyCount <= 4) {
      weights.emptyTiles *= 1.3;
    }

    return weights;
  }

  // 更新游戏阶段
  updateGamePhase(maxTile) {
    if (maxTile < 128) {
      this.gamePhase = 'early';
    } else if (maxTile < 1024) {
      this.gamePhase = 'mid';
    } else {
      this.gamePhase = 'late';
    }
    this.lastMaxTile = maxTile;
  }

  // 获取最佳移动 - 使用高级Expectimax算法
  getBestMove(board) {
    const startTime = Date.now();
    this.evaluationCount = 0;
    console.log('AI: Getting best move for board:', board);

    // 优先使用高级AI
    if (this.useAdvancedAI && this.advancedAI) {
      try {
        const move = this.advancedAI.getBestMove(board);
        const endTime = Date.now();
        console.log(`Advanced AI: Move ${move} selected in ${endTime - startTime}ms`);
        return move;
      } catch (error) {
        console.error('Advanced AI failed, falling back to original algorithm:', error);
        this.useAdvancedAI = false;
      }
    }

    // 验证输入
    if (!board || !Array.isArray(board) || board.length !== 4 ||
        !board.every(row => Array.isArray(row) && row.length === 4)) {
      console.error('AI: Invalid board format:', board);
      return null;
    }

    // 清理缓存（避免内存过度使用）
    if (this.cache.size > 10000) {
      this.cache.clear();
    }

    const moves = ['up', 'down', 'left', 'right'];
    let bestMove = null;
    let bestScore = -Infinity;
    const moveScores = {};

    // 检查是否有明显的最优移动
    const quickMove = this.getQuickOptimalMove(board);
    if (quickMove) {
      console.log('AI: Using quick optimal move:', quickMove);
      return quickMove;
    }

    for (const move of moves) {
      const newBoard = this.simulateMove(board, move);
      const isValidMove = !this.boardsEqual(board, newBoard);

      console.log(`AI: Testing move ${move}, valid: ${isValidMove}`);

      if (!isValidMove) {
        moveScores[move] = 'invalid';
        continue;
      }

      // 使用Expectimax算法
      const score = this.expectimax(newBoard, this.maxDepth - 1, false);
      moveScores[move] = score;

      console.log(`AI: Move ${move} score: ${score}`);

      if (score > bestScore) {
        bestScore = score;
        bestMove = move;
      }
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log('AI: All move scores:', moveScores);
    console.log('AI: Best move:', bestMove, 'with score:', bestScore);
    console.log(`AI: Performance - Time: ${duration}ms, Evaluations: ${this.evaluationCount}, Game Phase: ${this.gamePhase}`);
    console.log(`AI: Cache stats - hits: ${this.cacheHits}, misses: ${this.cacheMisses}`);

    // 如果没有找到最佳移动，使用蛇形策略
    if (!bestMove && this.useSimpleStrategy) {
      console.log('AI: Using snake strategy fallback');
      return this.getSnakeStrategyMove(board);
    }

    return bestMove;
  }

  // 改进的简单策略：基于角落策略
  getSimpleMove(board) {
    // 找到最大值的位置
    const maxValue = Math.max(...board.flat());
    let maxPos = null;

    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] === maxValue) {
          maxPos = { row: i, col: j };
          break;
        }
      }
      if (maxPos) break;
    }

    // 根据最大值位置选择优先移动方向
    let moves;
    if (maxPos) {
      if (maxPos.row <= 1 && maxPos.col <= 1) {
        // 最大值在左上区域，优先向左和向上
        moves = ['left', 'up', 'down', 'right'];
      } else if (maxPos.row <= 1 && maxPos.col >= 2) {
        // 最大值在右上区域，优先向右和向上
        moves = ['right', 'up', 'down', 'left'];
      } else if (maxPos.row >= 2 && maxPos.col <= 1) {
        // 最大值在左下区域，优先向左和向下
        moves = ['left', 'down', 'up', 'right'];
      } else {
        // 最大值在右下区域，优先向右和向下
        moves = ['right', 'down', 'up', 'left'];
      }
    } else {
      // 默认策略
      moves = ['left', 'down', 'right', 'up'];
    }

    for (const move of moves) {
      const newBoard = this.simulateMove(board, move);
      if (!this.boardsEqual(board, newBoard)) {
        console.log('AI: Simple strategy chose:', move);
        return move;
      }
    }

    console.log('AI: No valid moves found even with simple strategy');
    return null;
  }

  // 改进的简单策略
  getImprovedSimpleMove(board) {
    const maxValue = Math.max(...board.flat());

    // 基于最大值位置的智能策略
    let maxPos = null;
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] === maxValue) {
          maxPos = { row: i, col: j };
          break;
        }
      }
      if (maxPos) break;
    }

    // 优先级策略：尝试保持最大值在角落
    let moves;
    if (maxPos && maxPos.row === 0 && maxPos.col === 0) {
      // 最大值在左上角，优先向左和向上
      moves = ['left', 'up', 'down', 'right'];
    } else {
      // 尝试将最大值移动到左上角
      moves = ['up', 'left', 'down', 'right'];
    }

    for (const move of moves) {
      const newBoard = this.simulateMove(board, move);
      if (!this.boardsEqual(board, newBoard)) {
        console.log('AI: Improved strategy chose:', move);
        return move;
      }
    }

    console.log('AI: No valid moves found with improved strategy');
    return null;
  }

  // 快速检测明显的最优移动
  getQuickOptimalMove(board) {
    const maxValue = Math.max(...board.flat());
    let maxPos = null;

    // 找到最大值位置
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] === maxValue) {
          maxPos = { row: i, col: j };
          break;
        }
      }
      if (maxPos) break;
    }

    // 如果最大值不在左上角，优先移动到左上角
    if (maxPos && (maxPos.row !== 0 || maxPos.col !== 0)) {
      if (maxPos.row > 0) {
        const upBoard = this.simulateMove(board, 'up');
        if (!this.boardsEqual(board, upBoard)) {
          return 'up';
        }
      }
      if (maxPos.col > 0) {
        const leftBoard = this.simulateMove(board, 'left');
        if (!this.boardsEqual(board, leftBoard)) {
          return 'left';
        }
      }
    }

    // 检查是否有明显的合并机会
    const mergeMove = this.getObviousMergeMove(board);
    if (mergeMove) {
      return mergeMove;
    }

    return null;
  }

  // 检测明显的合并机会
  getObviousMergeMove(board) {
    const moves = ['up', 'down', 'left', 'right'];

    for (const move of moves) {
      const newBoard = this.simulateMove(board, move);
      if (this.boardsEqual(board, newBoard)) continue;

      // 计算合并获得的分数
      const scoreGain = this.getTotalScore(newBoard) - this.getTotalScore(board);

      // 如果能获得大量分数，这是一个好的合并
      if (scoreGain > 1000) {
        return move;
      }
    }

    return null;
  }

  // 蛇形策略移动
  getSnakeStrategyMove(board) {
    // 基于蛇形权重的策略
    const moves = ['left', 'up', 'down', 'right'];

    for (const move of moves) {
      const newBoard = this.simulateMove(board, move);
      if (!this.boardsEqual(board, newBoard)) {
        console.log('AI: Snake strategy chose:', move);
        return move;
      }
    }

    return null;
  }

  // 计算蛇形模式分数
  getSnakePatternScore(board) {
    let score = 0;

    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] > 0) {
          score += board[i][j] * this.snakeWeights[i][j];
        }
      }
    }

    return score;
  }

  // 计算位置权重分数
  getPositionWeightScore(board) {
    let score = 0;
    const maxValue = Math.max(...board.flat());

    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] > 0) {
          // 大数字在左上角获得更高分数
          const positionMultiplier = (4 - i) * (4 - j);
          const valueMultiplier = board[i][j] / maxValue;
          score += board[i][j] * positionMultiplier * valueMultiplier;
        }
      }
    }

    return score;
  }

  // 角落策略移动 - 专门针对高分优化
  getCornerStrategyMove(board) {
    const maxValue = Math.max(...board.flat());

    // 找到最大值的位置
    let maxPos = null;
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] === maxValue) {
          maxPos = { row: i, col: j };
          break;
        }
      }
      if (maxPos) break;
    }

    // 强制角落策略：最大值必须在左上角
    const targetCorner = { row: 0, col: 0 };

    if (maxPos && (maxPos.row !== targetCorner.row || maxPos.col !== targetCorner.col)) {
      // 计算移动到目标角落的路径
      if (maxPos.row > targetCorner.row) {
        // 需要向上移动
        const newBoard = this.simulateMove(board, 'up');
        if (!this.boardsEqual(board, newBoard)) {
          return 'up';
        }
      }
      if (maxPos.col > targetCorner.col) {
        // 需要向左移动
        const newBoard = this.simulateMove(board, 'left');
        if (!this.boardsEqual(board, newBoard)) {
          return 'left';
        }
      }
    }

    // 如果最大值已经在角落，使用蛇形模式策略
    const snakeMoves = ['left', 'down', 'right', 'up'];
    for (const move of snakeMoves) {
      const newBoard = this.simulateMove(board, move);
      if (!this.boardsEqual(board, newBoard)) {
        return move;
      }
    }

    return null;
  }

  // 高级 Expectimax 算法 - 基于研究的最佳实践
  expectimax(board, depth, isMaximizing) {
    // 检查缓存
    const boardKey = this.getBoardKey(board);
    const cacheKey = `${boardKey}-${depth}-${isMaximizing}`;

    if (this.cache.has(cacheKey)) {
      this.cacheHits++;
      return this.cache.get(cacheKey);
    }

    this.cacheMisses++;

    if (depth === 0 || this.isGameOver(board)) {
      const score = this.evaluateBoard(board);
      this.cache.set(cacheKey, score);
      return score;
    }

    if (isMaximizing) {
      let maxScore = -Infinity;
      const moves = ['up', 'down', 'left', 'right'];

      for (const move of moves) {
        const newBoard = this.simulateMove(board, move);
        if (this.boardsEqual(board, newBoard)) continue;

        const score = this.expectimax(newBoard, depth - 1, false);
        maxScore = Math.max(maxScore, score);
      }

      const result = maxScore === -Infinity ? this.evaluateBoard(board) : maxScore;
      this.cache.set(cacheKey, result);
      return result;
    } else {
      // 计算期望值 - 考虑随机瓦片生成的概率
      let expectedScore = 0;
      const emptyCells = this.getEmptyCells(board);

      if (emptyCells.length === 0) {
        const score = this.evaluateBoard(board);
        this.cache.set(cacheKey, score);
        return score;
      }

      // 智能选择要评估的空格 - 优先选择角落和边缘
      const prioritizedCells = this.prioritizeEmptyCells(emptyCells);
      const cellsToCheck = prioritizedCells.slice(0, Math.min(6, emptyCells.length));

      for (const cell of cellsToCheck) {
        // 90% 概率生成 2，10% 概率生成 4
        for (const { value, probability } of [{ value: 2, probability: 0.9 }, { value: 4, probability: 0.1 }]) {
          const newBoard = this.copyBoard(board);
          newBoard[cell.row][cell.col] = value;

          const score = this.expectimax(newBoard, depth - 1, true);
          expectedScore += score * probability;
        }
      }

      const result = expectedScore / cellsToCheck.length;
      this.cache.set(cacheKey, result);
      return result;
    }
  }

  // 获取棋盘的唯一键值（用于缓存）
  getBoardKey(board) {
    return board.flat().join(',');
  }

  // 优先选择空格 - 角落和边缘优先
  prioritizeEmptyCells(emptyCells) {
    return emptyCells.sort((a, b) => {
      const scoreA = this.getCellPriority(a.row, a.col);
      const scoreB = this.getCellPriority(b.row, b.col);
      return scoreB - scoreA; // 降序排列
    });
  }

  // 获取格子的优先级分数
  getCellPriority(row, col) {
    // 角落最高优先级
    if ((row === 0 || row === 3) && (col === 0 || col === 3)) {
      return 10;
    }
    // 边缘次高优先级
    if (row === 0 || row === 3 || col === 0 || col === 3) {
      return 5;
    }
    // 中心最低优先级
    return 1;
  }

  // 快速启发式移动
  getQuickMove(board) {
    const maxValue = Math.max(...board.flat());

    // 如果最大值不在角落，优先移动到角落
    if (maxValue >= 128) {
      const corners = [
        { pos: [0, 0], value: board[0][0] },
        { pos: [0, 3], value: board[0][3] },
        { pos: [3, 0], value: board[3][0] },
        { pos: [3, 3], value: board[3][3] }
      ];

      const maxInCorner = corners.some(corner => corner.value === maxValue);

      if (!maxInCorner) {
        // 尝试将最大值移动到左上角
        if (board[0][0] !== maxValue) {
          const moves = ['up', 'left'];
          for (const move of moves) {
            const newBoard = this.simulateMove(board, move);
            if (!this.boardsEqual(board, newBoard)) {
              return move;
            }
          }
        }
      }
    }

    return null;
  }

  // 高级评估棋盘状态函数 - 使用动态权重系统
  evaluateBoard(board) {
    this.evaluationCount++;

    // 获取动态权重
    const weights = this.getDynamicWeights(board);

    let score = 0;

    // 空格数量权重 - 使用平方增强重要性
    const emptyCells = this.getEmptyCells(board).length;
    score += Math.pow(emptyCells + 1, 2) * weights.emptyTiles;

    // 使用改进的单调性评估
    score += this.getAdvancedMonotonicity(board) * weights.monotonicity;

    // 使用原始平滑性评估（更稳定）
    score += this.getSmoothness(board) * weights.smoothness;

    // 使用原始角落奖励（更稳定）
    score += this.getCornerBonus(board) * weights.cornerBonus;

    // 边缘奖励 - 大数字在边缘更好
    score += this.getEdgeBonus(board) * weights.edgeBonus;

    // 使用改进的合并潜力
    score += this.getAdvancedMergePotential(board) * weights.mergeBonus;

    // 使用原始蛇形模式（更稳定）
    score += this.getSnakePatternScore(board) * weights.snakePattern;

    // 最大瓦片位置奖励（简化版）
    score += this.getMaxTilePositionBonus(board) * weights.maxTileBonus;

    // 总分权重（使用对数避免过度影响）
    score += Math.log2(this.getTotalScore(board) + 1) * 150;

    // 严重惩罚游戏结束状态
    if (this.isGameOver(board)) {
      score -= 1000000; // 适度惩罚
    }

    // 奖励高价值瓦片（使用平方根）
    const maxValue = Math.max(...board.flat());
    score += Math.sqrt(maxValue) * 100;

    return score;
  }

  // 改进的单调性分数 - 更精确的计算
  getMonotonicity(board) {
    let score = 0;

    // 检查行的单调性 - 使用对数值进行比较
    for (let row = 0; row < 4; row++) {
      let increasing = 0, decreasing = 0;
      for (let col = 0; col < 3; col++) {
        const current = board[row][col] === 0 ? 0 : Math.log2(board[row][col]);
        const next = board[row][col + 1] === 0 ? 0 : Math.log2(board[row][col + 1]);

        if (current > next) {
          decreasing += current - next;
        } else if (current < next) {
          increasing += next - current;
        }
      }
      score += Math.max(increasing, decreasing);
    }

    // 检查列的单调性
    for (let col = 0; col < 4; col++) {
      let increasing = 0, decreasing = 0;
      for (let row = 0; row < 3; row++) {
        const current = board[row][col] === 0 ? 0 : Math.log2(board[row][col]);
        const next = board[row + 1][col] === 0 ? 0 : Math.log2(board[row + 1][col]);

        if (current > next) {
          decreasing += current - next;
        } else if (current < next) {
          increasing += next - current;
        }
      }
      score += Math.max(increasing, decreasing);
    }

    return score;
  }

  // 获取平滑性分数
  getSmoothness(board) {
    let score = 0;

    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        if (board[row][col] === 0) continue;

        const value = Math.log2(board[row][col]);

        // 检查右邻居
        if (col < 3 && board[row][col + 1] !== 0) {
          const neighborValue = Math.log2(board[row][col + 1]);
          score -= Math.abs(value - neighborValue);
        }

        // 检查下邻居
        if (row < 3 && board[row + 1][col] !== 0) {
          const neighborValue = Math.log2(board[row + 1][col]);
          score -= Math.abs(value - neighborValue);
        }
      }
    }

    return score;
  }

  // 改进的角落奖励
  getCornerBonus(board) {
    const maxValue = Math.max(...board.flat());
    const corners = [
      { value: board[0][0], weight: 1.0 },
      { value: board[0][3], weight: 0.8 },
      { value: board[3][0], weight: 0.8 },
      { value: board[3][3], weight: 0.6 }
    ];

    let bonus = 0;
    for (const corner of corners) {
      if (corner.value === maxValue) {
        bonus += maxValue * corner.weight;
      }
      // 给大数字在角落额外奖励
      if (corner.value >= 64) {
        bonus += corner.value * corner.weight * 0.1;
      }
    }

    return bonus;
  }

  // 边缘奖励 - 大数字在边缘更好
  getEdgeBonus(board) {
    let bonus = 0;

    // 检查边缘位置
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] > 0 && (i === 0 || i === 3 || j === 0 || j === 3)) {
          // 边缘位置的大数字获得奖励
          if (board[i][j] >= 32) {
            bonus += board[i][j] * 0.1;
          }
        }
      }
    }

    return bonus;
  }

  // 合并潜力 - 相邻相同数字的奖励
  getMergePotential(board) {
    let potential = 0;

    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] === 0) continue;

        const value = board[i][j];

        // 检查右邻居
        if (j < 3 && board[i][j + 1] === value) {
          potential += value;
        }

        // 检查下邻居
        if (i < 3 && board[i + 1][j] === value) {
          potential += value;
        }
      }
    }

    return potential;
  }



  // 最大瓦片位置评估
  getMaxTilePositionScore(board) {
    const maxValue = Math.max(...board.flat());

    // 找到最大值的位置
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] === maxValue) {
          // 最大值在左上角得到最高分
          if (i === 0 && j === 0) {
            return maxValue * 2;
          }
          // 最大值在其他角落得到较高分
          else if ((i === 0 && j === 3) || (i === 3 && j === 0) || (i === 3 && j === 3)) {
            return maxValue * 1.5;
          }
          // 最大值在边缘得到中等分
          else if (i === 0 || i === 3 || j === 0 || j === 3) {
            return maxValue;
          }
          // 最大值在中间得到负分
          else {
            return -maxValue;
          }
        }
      }
    }

    return 0;
  }

  // 获取总分
  getTotalScore(board) {
    return board.flat().reduce((sum, val) => sum + val, 0);
  }

  // 模拟移动
  simulateMove(board, direction) {
    const newBoard = this.copyBoard(board);

    switch (direction) {
      case 'left':
        return this.moveLeft(newBoard);
      case 'right':
        return this.moveRight(newBoard);
      case 'up':
        return this.moveUp(newBoard);
      case 'down':
        return this.moveDown(newBoard);
      default:
        return newBoard;
    }
  }

  // 向左移动
  moveLeft(board) {
    for (let row = 0; row < 4; row++) {
      const line = board[row].filter(val => val !== 0);

      for (let i = 0; i < line.length - 1; i++) {
        if (line[i] === line[i + 1]) {
          line[i] *= 2;
          line[i + 1] = 0;
        }
      }

      const newLine = line.filter(val => val !== 0);
      while (newLine.length < 4) newLine.push(0);

      board[row] = newLine;
    }

    return board;
  }

  // 向右移动
  moveRight(board) {
    for (let row = 0; row < 4; row++) {
      board[row].reverse();
    }
    this.moveLeft(board);
    for (let row = 0; row < 4; row++) {
      board[row].reverse();
    }
    return board;
  }

  // 向上移动
  moveUp(board) {
    const transposed = this.transpose(board);
    this.moveLeft(transposed);
    return this.transpose(transposed);
  }

  // 向下移动
  moveDown(board) {
    const transposed = this.transpose(board);
    this.moveRight(transposed);
    return this.transpose(transposed);
  }

  // 转置矩阵
  transpose(board) {
    const result = Array(4).fill().map(() => Array(4).fill(0));
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        result[j][i] = board[i][j];
      }
    }
    return result;
  }

  // 复制棋盘
  copyBoard(board) {
    return board.map(row => [...row]);
  }

  // 检查两个棋盘是否相等
  boardsEqual(board1, board2) {
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board1[i][j] !== board2[i][j]) return false;
      }
    }
    return true;
  }

  // 获取空格位置
  getEmptyCells(board) {
    const emptyCells = [];
    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        if (board[row][col] === 0) {
          emptyCells.push({ row, col });
        }
      }
    }
    return emptyCells;
  }

  // 检查游戏是否结束
  isGameOver(board) {
    // 检查是否有空格
    if (this.getEmptyCells(board).length > 0) return false;

    // 检查是否可以合并
    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        const current = board[row][col];

        // 检查右邻居
        if (col < 3 && current === board[row][col + 1]) return false;

        // 检查下邻居
        if (row < 3 && current === board[row + 1][col]) return false;
      }
    }

    return true;
  }

  // 高级单调性评估
  getAdvancedMonotonicity(board) {
    let totalScore = 0;

    // 行单调性
    for (let row = 0; row < 4; row++) {
      let leftRight = 0, rightLeft = 0;
      for (let col = 0; col < 3; col++) {
        const left = board[row][col] === 0 ? 0 : Math.log2(board[row][col]);
        const right = board[row][col + 1] === 0 ? 0 : Math.log2(board[row][col + 1]);

        if (left >= right && left !== 0) leftRight += Math.pow(left - right, 2);
        if (right >= left && right !== 0) rightLeft += Math.pow(right - left, 2);
      }
      totalScore += Math.max(leftRight, rightLeft);
    }

    // 列单调性
    for (let col = 0; col < 4; col++) {
      let topBottom = 0, bottomTop = 0;
      for (let row = 0; row < 3; row++) {
        const top = board[row][col] === 0 ? 0 : Math.log2(board[row][col]);
        const bottom = board[row + 1][col] === 0 ? 0 : Math.log2(board[row + 1][col]);

        if (top >= bottom && top !== 0) topBottom += Math.pow(top - bottom, 2);
        if (bottom >= top && bottom !== 0) bottomTop += Math.pow(bottom - top, 2);
      }
      totalScore += Math.max(topBottom, bottomTop);
    }

    return totalScore;
  }

  // 高级平滑性评估
  getAdvancedSmoothness(board) {
    let smoothness = 0;

    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        if (board[row][col] !== 0) {
          const value = Math.log2(board[row][col]);
          let numNeighbors = 0;
          let sum = 0;

          // 检查四个方向的邻居
          const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]];
          for (const [dr, dc] of directions) {
            const newRow = row + dr;
            const newCol = col + dc;

            if (newRow >= 0 && newRow < 4 && newCol >= 0 && newCol < 4) {
              if (board[newRow][newCol] !== 0) {
                const neighborValue = Math.log2(board[newRow][newCol]);
                sum += Math.abs(value - neighborValue);
                numNeighbors++;
              }
            }
          }

          if (numNeighbors > 0) {
            smoothness -= sum / numNeighbors;
          }
        }
      }
    }

    return smoothness;
  }

  // 动态角落奖励
  getDynamicCornerBonus(board) {
    const maxValue = Math.max(...board.flat());
    let maxBonus = 0;

    // 检查四个角落
    const corners = [[0, 0], [0, 3], [3, 0], [3, 3]];
    for (const [row, col] of corners) {
      if (board[row][col] === maxValue) {
        maxBonus = Math.log2(maxValue) * 1000;
        break;
      }
    }

    return maxBonus;
  }

  // 高级合并潜力
  getAdvancedMergePotential(board) {
    let potential = 0;

    // 检查水平合并
    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 3; col++) {
        if (board[row][col] !== 0 && board[row][col] === board[row][col + 1]) {
          potential += board[row][col] * 2;
        }
      }
    }

    // 检查垂直合并
    for (let col = 0; col < 4; col++) {
      for (let row = 0; row < 3; row++) {
        if (board[row][col] !== 0 && board[row][col] === board[row + 1][col]) {
          potential += board[row][col] * 2;
        }
      }
    }

    return potential;
  }

  // 动态蛇形评分
  getDynamicSnakeScore(board) {
    const maxValue = Math.max(...board.flat());

    // 根据最大值选择蛇形模式
    const weights = maxValue >= 1024 ? this.snakeWeights : this.alternateSnakeWeights;

    let score = 0;
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] > 0) {
          score += Math.log2(board[i][j]) * weights[i][j];
        }
      }
    }

    return score;
  }

  // 最大瓦片位置奖励
  getMaxTilePositionBonus(board) {
    const maxValue = Math.max(...board.flat());
    let bonus = 0;

    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] === maxValue) {
          // 奖励角落和边缘位置
          if ((i === 0 || i === 3) && (j === 0 || j === 3)) {
            bonus += maxValue * 10; // 角落
          } else if (i === 0 || i === 3 || j === 0 || j === 3) {
            bonus += maxValue * 5;  // 边缘
          }
        }
      }
    }

    return bonus;
  }

  // 梯度评分
  getGradientScore(board) {
    let score = 0;

    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] > 0) {
          const value = Math.log2(board[i][j]);
          // 从左上角到右下角的梯度权重
          const gradientWeight = (4 - i) + (4 - j);
          score += value * gradientWeight;
        }
      }
    }

    return score;
  }

  // 孤立惩罚
  getIsolationPenalty(board) {
    let penalty = 0;

    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (board[i][j] > 0) {
          let neighbors = 0;
          const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]];

          for (const [di, dj] of directions) {
            const ni = i + di;
            const nj = j + dj;
            if (ni >= 0 && ni < 4 && nj >= 0 && nj < 4 && board[ni][nj] > 0) {
              neighbors++;
            }
          }

          // 惩罚孤立的高价值瓦片
          if (neighbors === 0 && board[i][j] >= 64) {
            penalty += board[i][j];
          }
        }
      }
    }

    return penalty;
  }
}

class Game2048Detector {
  constructor() {
    this.gameContainer = null;
    this.gameBoard = null;
    this.isAutoPlaying = false;
    this.autoPlayInterval = null;
    this.moveSpeed = 100; // 更快的移动速度
    this.ai = new Game2048AI();
  }

  // 检测页面上是否有2048游戏
  detectGame() {
    console.log('Starting game detection...');

    // 特殊处理：如果是我们的测试页面，直接返回成功
    if (window.game && typeof window.game.board !== 'undefined') {
      console.log('Detected test page with window.game object');
      this.gameContainer = document.querySelector('.game-container');
      this.gameBoard = this.gameContainer;
      return true;
    }

    // 常见的2048游戏容器选择器
    const selectors = [
      '.game-container',
      '#game-container',
      '.container',
      '.grid-container',
      '.game-board',
      '[class*="game"]',
      '[class*="2048"]',
      '[id*="game"]',
      '[id*="2048"]'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      console.log(`Checking selector ${selector}:`, element);
      if (element && this.isValidGameContainer(element)) {
        console.log('Found game container with selector:', selector);
        this.gameContainer = element;
        this.gameBoard = this.findGameBoard(element);
        return true;
      }
    }

    // 如果没找到，尝试通过文本内容查找
    const elements = document.querySelectorAll('div');
    console.log('Checking', elements.length, 'div elements for game content...');

    for (const element of elements) {
      if (this.containsGameElements(element)) {
        console.log('Found game elements in div');
        this.gameContainer = element;
        this.gameBoard = this.findGameBoard(element);
        return true;
      }
    }

    // 最后尝试：检查是否有数字瓦片（更宽松的检测）
    const tiles = document.querySelectorAll('[class*="tile"], .tile, [class*="cell"], .cell');
    console.log('Found', tiles.length, 'potential tiles');

    if (tiles.length >= 2) {
      const hasValidNumbers = Array.from(tiles).some(tile => {
        const text = tile.textContent.trim();
        return /^(2|4|8|16|32|64|128|256|512|1024|2048|4096)$/.test(text);
      });

      if (hasValidNumbers) {
        console.log('Found valid game numbers, assuming this is a 2048 game');
        this.gameContainer = document.body;
        this.gameBoard = document.body;
        return true;
      }
    }

    console.log('No 2048 game detected');
    return false;
  }

  // 验证是否是有效的游戏容器
  isValidGameContainer(element) {
    const text = element.textContent.toLowerCase();
    const hasGameKeywords = text.includes('2048') || text.includes('score') || text.includes('best') || text.includes('游戏');
    const tiles = element.querySelectorAll('.grid-cell, .tile, [class*="cell"], [class*="tile"]');
    const hasGridStructure = tiles.length >= 4;

    // 检查是否有有效的数字
    const hasValidNumbers = Array.from(tiles).some(tile => {
      const text = tile.textContent.trim();
      return /^(2|4|8|16|32|64|128|256|512|1024|2048|4096)$/.test(text);
    });

    console.log('Container validation:', {
      hasGameKeywords,
      hasGridStructure,
      hasValidNumbers,
      tilesCount: tiles.length
    });

    // 检查是否有游戏容器的类名
    const hasGameContainerClass = element.classList.contains('game-container') ||
                                  element.classList.contains('container') ||
                                  element.id === 'game-container';

    console.log('Additional checks:', {
      hasGameContainerClass,
      className: element.className,
      id: element.id
    });

    // 更宽松的检测条件
    return hasGameContainerClass || hasGameKeywords || (hasGridStructure && hasValidNumbers) || hasGridStructure;
  }

  // 检查元素是否包含游戏元素
  containsGameElements(element) {
    const tiles = element.querySelectorAll('.tile, [class*="tile"], .grid-cell, [class*="cell"]');
    const hasNumbers = Array.from(tiles).some(tile => {
      const text = tile.textContent.trim();
      return /^(2|4|8|16|32|64|128|256|512|1024|2048|4096)$/.test(text);
    });

    console.log('Element check:', {
      tilesCount: tiles.length,
      hasNumbers
    });

    return tiles.length >= 2 && hasNumbers;
  }

  // 查找游戏板
  findGameBoard(container) {
    const boardSelectors = [
      '.grid-container',
      '.game-board', 
      '.board',
      '[class*="grid"]',
      '[class*="board"]'
    ];

    for (const selector of boardSelectors) {
      const board = container.querySelector(selector);
      if (board) return board;
    }

    return container;
  }

  // 获取当前游戏状态
  getGameState() {
    console.log('Getting game state...');

    // 首先尝试从 window.game 直接获取（如果是我们的测试页面）
    if (window.game && window.game.board) {
      const board = window.game.board;
      console.log('Got game state from window.game.board:', board);

      // 验证board格式
      if (Array.isArray(board) && board.length === 4 &&
          board.every(row => Array.isArray(row) && row.length === 4)) {
        return board;
      } else {
        console.error('Invalid board format from window.game.board:', board);
        return null;
      }
    }

    if (!this.gameBoard) {
      console.log('No game board found');
      return null;
    }

    // 尝试多种选择器来查找瓦片（优先查找实际的数字瓦片，而不是空格子）
    const tileSelectors = [
      '.tile:not(.tile-new):not(.tile-merged)', // 排除动画瓦片
      '.tile',                                   // 基本瓦片选择器
      '.tile-container .tile',                   // 瓦片容器中的瓦片
      '[class*="tile-"]:not([class*="tile-container"]):not([class*="tile-new"]):not([class*="tile-merged"])', // 包含tile-的类但排除容器和动画
      '[class*="tile"]'                          // 任何包含tile的类
    ];

    let tiles = [];
    for (const selector of tileSelectors) {
      tiles = this.gameBoard.querySelectorAll(selector);
      console.log(`Trying selector "${selector}": found ${tiles.length} elements`);

      // 过滤掉空的瓦片（没有文本内容或值为0的）
      const validTiles = Array.from(tiles).filter(tile => {
        const text = tile.textContent.trim();
        const value = parseInt(text) || 0;
        return value > 0;
      });

      console.log(`Valid tiles with values: ${validTiles.length}`);

      if (validTiles.length > 0) {
        tiles = validTiles;
        break;
      }
    }

    // 如果还是没找到，尝试在整个文档中查找
    if (tiles.length === 0) {
      const allTiles = document.querySelectorAll('.tile, [class*="tile"]');
      tiles = Array.from(allTiles).filter(tile => {
        const text = tile.textContent.trim();
        const value = parseInt(text) || 0;
        return value > 0;
      });
      console.log(`Searching entire document: found ${tiles.length} valid tiles`);
    }

    console.log('Found', tiles.length, 'tiles');

    const board = Array(4).fill().map(() => Array(4).fill(0));

    tiles.forEach((tile, index) => {
      const text = tile.textContent.trim();
      const value = parseInt(text) || 0;

      console.log(`Tile ${index}: text="${text}", value=${value}, element:`, tile);

      if (value > 0 && value <= 4096) { // 确保是有效的2048数字
        const position = this.getTilePosition(tile);
        console.log(`Tile ${index} position:`, position);

        if (position && position.row >= 0 && position.row < 4 && position.col >= 0 && position.col < 4) {
          board[position.row][position.col] = value;
          console.log(`Placed ${value} at [${position.row}][${position.col}]`);
        }
      }
    });

    console.log('Final board state:', board);
    return board;
  }

  // 获取瓦片位置
  getTilePosition(tile) {
    console.log('Getting position for tile:', tile, 'classes:', tile.className);

    const classList = Array.from(tile.classList);

    // 尝试从类名中解析位置
    for (const className of classList) {
      const match = className.match(/(?:tile-position-|grid-cell-)(\d+)-(\d+)/);
      if (match) {
        const pos = { row: parseInt(match[2]) - 1, col: parseInt(match[1]) - 1 };
        console.log('Position from class name:', pos);
        return pos;
      }
    }

    // 尝试从样式中解析位置
    const style = window.getComputedStyle(tile);
    const transform = style.transform;
    console.log('Transform style:', transform);

    if (transform && transform !== 'none') {
      const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
      if (match) {
        const x = parseInt(match[1]);
        const y = parseInt(match[2]);
        // 假设每个格子的大小是固定的，这里需要根据实际情况调整
        const cellSize = 70; // 常见的格子大小
        const pos = {
          row: Math.round(y / cellSize),
          col: Math.round(x / cellSize)
        };
        console.log('Position from transform:', pos);
        return pos;
      }
    }

    // 尝试从绝对位置计算（针对我们的测试页面）
    const rect = tile.getBoundingClientRect();
    const containerRect = this.gameBoard ? this.gameBoard.getBoundingClientRect() : document.body.getBoundingClientRect();

    console.log('Tile rect:', rect);
    console.log('Container rect:', containerRect);

    if (rect && containerRect) {
      const relativeX = rect.left - containerRect.left;
      const relativeY = rect.top - containerRect.top;

      // 基于我们测试页面的布局计算
      const cellSize = 70; // 60px + 10px margin
      const col = Math.round(relativeX / cellSize);
      const row = Math.round(relativeY / cellSize);

      console.log(`Position calculation: relativeX=${relativeX}, relativeY=${relativeY}, row=${row}, col=${col}`);

      if (row >= 0 && row < 4 && col >= 0 && col < 4) {
        return { row, col };
      }
    }

    // 如果所有方法都失败，尝试简单的索引方法
    const allTiles = Array.from(document.querySelectorAll('.tile, [class*="tile"]'));
    const index = allTiles.indexOf(tile);
    if (index >= 0 && index < 16) {
      const pos = {
        row: Math.floor(index / 4),
        col: index % 4
      };
      console.log('Position from index:', pos);
      return pos;
    }

    console.log('Could not determine tile position');
    return null;
  }

  // 执行移动操作
  makeMove(direction) {
    console.log('Making move:', direction);

    const keyMap = {
      'up': { key: 'ArrowUp', keyCode: 38, code: 'ArrowUp' },
      'down': { key: 'ArrowDown', keyCode: 40, code: 'ArrowDown' },
      'left': { key: 'ArrowLeft', keyCode: 37, code: 'ArrowLeft' },
      'right': { key: 'ArrowRight', keyCode: 39, code: 'ArrowRight' }
    };

    const keyInfo = keyMap[direction];
    if (!keyInfo) {
      console.log('Invalid direction:', direction);
      return false;
    }

    // 创建多种类型的键盘事件
    const eventOptions = {
      key: keyInfo.key,
      code: keyInfo.code,
      keyCode: keyInfo.keyCode,
      which: keyInfo.keyCode,
      bubbles: true,
      cancelable: true,
      composed: true
    };

    // 创建 keydown 事件
    const keydownEvent = new KeyboardEvent('keydown', eventOptions);

    // 创建 keyup 事件
    const keyupEvent = new KeyboardEvent('keyup', eventOptions);

    console.log('Dispatching keyboard events...');

    // 尝试多个目标元素
    const targets = [
      document,
      document.body,
      this.gameContainer,
      this.gameBoard,
      window
    ].filter(target => target);

    let eventDispatched = false;

    for (const target of targets) {
      try {
        if (target === window) {
          // 对于 window 对象，使用不同的方法
          window.dispatchEvent(keydownEvent);
          setTimeout(() => window.dispatchEvent(keyupEvent), 10);
        } else {
          target.dispatchEvent(keydownEvent);
          setTimeout(() => target.dispatchEvent(keyupEvent), 10);
        }
        eventDispatched = true;
        console.log('Event dispatched to:', target.tagName || target.constructor.name);
      } catch (error) {
        console.log('Failed to dispatch to target:', error);
      }
    }

    // 额外尝试：直接调用游戏的处理函数（如果能找到的话）
    try {
      // 查找可能的游戏实例
      if (window.game && typeof window.game.handleKeyPress === 'function') {
        console.log('Found game instance, calling handleKeyPress directly');
        window.game.handleKeyPress({ key: keyInfo.key, preventDefault: () => {} });
      }
    } catch (error) {
      console.log('Direct game call failed:', error);
    }

    console.log('Move completed, event dispatched:', eventDispatched);
    return eventDispatched;
  }

  // 开始自动游戏
  startAutoPlay(speed = 100) {
    if (this.isAutoPlaying) return false;
    
    this.isAutoPlaying = true;
    this.moveSpeed = speed;
    
    chrome.runtime.sendMessage({
      action: 'updateBadge',
      text: 'ON',
      color: '#4CAF50'
    });

    this.autoPlayLoop();
    return true;
  }

  // 停止自动游戏
  stopAutoPlay() {
    this.isAutoPlaying = false;
    if (this.autoPlayInterval) {
      clearTimeout(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }
    
    chrome.runtime.sendMessage({
      action: 'updateBadge',
      text: '',
      color: '#666'
    });

    return true;
  }

  // 自动游戏循环
  autoPlayLoop() {
    if (!this.isAutoPlaying) {
      console.log('Auto play stopped, exiting loop');
      return;
    }

    console.log('Auto play loop iteration');
    const gameState = this.getGameState();

    if (!gameState) {
      console.log('Could not get game state, stopping auto play');
      this.stopAutoPlay();
      return;
    }

    console.log('Current game state:', gameState);

    // 检查游戏是否结束
    if (this.ai.isGameOver(gameState)) {
      console.log('Game over detected');
      this.stopAutoPlay();
      chrome.runtime.sendMessage({
        action: 'updateStatus',
        type: 'error',
        message: '游戏结束'
      });
      return;
    }

    // 获取最佳移动
    const bestMove = this.ai.getBestMove(gameState);
    console.log('Best move calculated:', bestMove);

    if (bestMove) {
      const moveSuccess = this.makeMove(bestMove);
      console.log('Move execution result:', moveSuccess);

      // 继续下一次移动
      this.autoPlayInterval = setTimeout(() => {
        this.autoPlayLoop();
      }, this.moveSpeed);
    } else {
      console.log('No valid moves available');
      this.stopAutoPlay();
      chrome.runtime.sendMessage({
        action: 'updateStatus',
        type: 'error',
        message: '无可用移动'
      });
    }
  }
}

// 游戏检测器实例
const gameDetector = new Game2048Detector();

// 测试函数
function testAI() {
  console.log('=== AI Test ===');
  const testBoard = [
    [2, 4, 8, 16],
    [0, 0, 0, 0],
    [0, 0, 0, 0],
    [0, 0, 0, 0]
  ];

  console.log('Test board:', testBoard);
  const ai = new Game2048AI();
  const bestMove = ai.getBestMove(testBoard);
  console.log('AI suggested move:', bestMove);

  // 测试移动模拟
  const moves = ['left', 'right', 'up', 'down'];
  moves.forEach(move => {
    const result = ai.simulateMove(testBoard, move);
    const isValid = !ai.boardsEqual(testBoard, result);
    console.log(`Move ${move}: valid=${isValid}, result:`, result);
  });
}

// 手动测试移动函数
function testMove(direction) {
  console.log('=== Manual Move Test ===');
  console.log('Testing move:', direction);
  const result = gameDetector.makeMove(direction);
  console.log('Move result:', result);
  return result;
}

// 添加到window对象以便在控制台调用
window.testAI = testAI;
window.testMove = testMove;
window.gameDetector = gameDetector;

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Content script received message:', request);

  try {
    switch (request.action) {
      case 'checkGame':
        // 添加延迟重试机制
        setTimeout(() => {
          const gameFound = gameDetector.detectGame();
          console.log('Game detection result:', gameFound);
          sendResponse({ gameFound });
        }, 500); // 等待500ms让页面完全加载
        return true; // 异步响应
        break;

      case 'startAutoPlay':
        const started = gameDetector.startAutoPlay(request.speed);
        console.log('Start auto play result:', started);
        sendResponse({ success: started });
        break;

      case 'stopAutoPlay':
        const stopped = gameDetector.stopAutoPlay();
        console.log('Stop auto play result:', stopped);
        sendResponse({ success: stopped });
        break;

      default:
        console.log('Unknown action:', request.action);
        sendResponse({ success: false });
    }
  } catch (error) {
    console.error('Error in content script:', error);
    sendResponse({ success: false, error: error.message });
  }

  // 返回 true 表示我们会异步发送响应
  return true;
});

// 页面加载完成后自动检测游戏
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => gameDetector.detectGame(), 1000);
  });
} else {
  setTimeout(() => gameDetector.detectGame(), 1000);
}

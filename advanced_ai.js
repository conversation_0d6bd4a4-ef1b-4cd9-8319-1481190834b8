// 高级2048 AI算法 - 基于成熟的Expectimax实现
// 改编自 azaky/2048-AI 项目

class Advanced2048AI {
  constructor() {
    this.algorithm = "expectimax";
    this.depthlimit = 4; // 适中的搜索深度，平衡性能和质量
    this.memo = []; // 记忆化缓存
  }

  // 主要接口：获取最佳移动
  getBestMove(board) {
    console.log('Advanced AI: Getting best move for board:', board);

    try {
      // 转换棋盘格式
      const grid = this.convertBoardToGrid(board);
      console.log('Advanced AI: Grid converted successfully');

      // 清理记忆化缓存
      this.memo = [];

      let move = -1;
      let best = this.getMoveExpectimax(grid);
      move = best.move;

      console.log(`Advanced AI: Best move: ${move}, score: ${best.score}`);

      // 转换移动方向
      const directions = ['up', 'right', 'down', 'left'];
      const result = directions[move] || 'up';
      console.log(`Advanced AI: Returning direction: ${result}`);
      return result;
    } catch (error) {
      console.error('Advanced AI: Error in getBestMove:', error);
      // 返回一个安全的默认移动
      return 'up';
    }
  }

  // 将我们的board格式转换为AI算法需要的grid格式
  convertBoardToGrid(board) {
    const grid = {
      size: 4,
      cells: []
    };
    
    // 初始化cells数组
    for (let x = 0; x < 4; x++) {
      grid.cells[x] = [];
      for (let y = 0; y < 4; y++) {
        const value = board[x][y];
        grid.cells[x][y] = value > 0 ? { value: value } : null;
      }
    }
    
    // 添加必要的方法
    grid.clone = function() {
      const newGrid = {
        size: 4,
        cells: []
      };
      
      for (let x = 0; x < 4; x++) {
        newGrid.cells[x] = [];
        for (let y = 0; y < 4; y++) {
          newGrid.cells[x][y] = this.cells[x][y] ? { value: this.cells[x][y].value } : null;
        }
      }
      
      // 复制方法
      newGrid.clone = this.clone;
      newGrid.move = this.move;
      newGrid.availableCells = this.availableCells;
      newGrid.insertTile = this.insertTile;
      newGrid.eachCell = this.eachCell;
      newGrid.largest = this.largest;
      
      return newGrid;
    };
    
    grid.availableCells = function() {
      const cells = [];
      for (let x = 0; x < 4; x++) {
        for (let y = 0; y < 4; y++) {
          if (!this.cells[x][y]) {
            cells.push({ x: x, y: y });
          }
        }
      }
      return cells;
    };
    
    grid.insertTile = function(tile) {
      this.cells[tile.position.x][tile.position.y] = { value: tile.value };
    };
    
    grid.eachCell = function(callback) {
      for (let x = 0; x < 4; x++) {
        for (let y = 0; y < 4; y++) {
          callback(x, y, this.cells[x][y]);
        }
      }
    };
    
    grid.largest = function() {
      let max = 0;
      this.eachCell(function(x, y, cell) {
        if (cell && cell.value > max) {
          max = cell.value;
        }
      });
      return max;
    };
    
    grid.move = function(direction) {
      // 实现移动逻辑
      const vectors = [
        { x: -1, y: 0 }, // up
        { x: 0, y: 1 },  // right
        { x: 1, y: 0 },  // down
        { x: 0, y: -1 }  // left
      ];
      
      const vector = vectors[direction];

      // 构建遍历顺序
      const traversals = { x: [], y: [] };
      for (let pos = 0; pos < 4; pos++) {
        traversals.x.push(pos);
        traversals.y.push(pos);
      }
      // 根据移动方向调整遍历顺序
      if (vector.x === 1) traversals.x = traversals.x.reverse();
      if (vector.y === 1) traversals.y = traversals.y.reverse();
      let moved = false;
      let score = 0;
      
      // 保存原始状态
      const originalCells = [];
      for (let x = 0; x < 4; x++) {
        originalCells[x] = [];
        for (let y = 0; y < 4; y++) {
          originalCells[x][y] = this.cells[x][y] ? { value: this.cells[x][y].value } : null;
        }
      }
      
      // 清除合并标记
      this.eachCell(function(x, y, cell) {
        if (cell) {
          cell.mergedFrom = null;
        }
      });
      
      // 遍历网格
      for (let x of traversals.x) {
        for (let y of traversals.y) {
          const cell = this.cells[x][y];
          
          if (cell) {
            // 找到最远位置
            let previous;
            let current = { x: x, y: y };

            do {
              previous = current;
              current = { x: previous.x + vector.x, y: previous.y + vector.y };
            } while (current.x >= 0 && current.x < 4 && current.y >= 0 && current.y < 4 &&
                     !this.cells[current.x][current.y]);

            const positions = {
              farthest: previous,
              next: current
            };

            const next = (positions.next.x >= 0 && positions.next.x < 4 &&
                         positions.next.y >= 0 && positions.next.y < 4) ?
                         this.cells[positions.next.x][positions.next.y] : null;

            // 检查是否可以合并
            if (next && next.value === cell.value && !next.mergedFrom) {
              const merged = { value: cell.value * 2 };
              merged.mergedFrom = [cell, next];

              this.cells[positions.next.x][positions.next.y] = merged;
              this.cells[x][y] = null;

              score += merged.value;
              moved = true;
            } else {
              // 移动瓦片
              if (positions.farthest.x !== x || positions.farthest.y !== y) {
                this.cells[positions.farthest.x][positions.farthest.y] = cell;
                this.cells[x][y] = null;
                moved = true;
              }
            }
          }
        }
      }
      
      return moved ? score : -1;
    };
    

    
    return grid;
  }

  // Expectimax算法主函数
  getMoveExpectimax(grid) {
    return this.getMoveExpectimaxDFS(grid, this.depthlimit);
  }

  // 记忆化相关函数
  getHashCode(grid) {
    const p = 982451653;
    let hash = 0;
    grid.eachCell(function(x, y, cell) {
      const addum = cell ? cell.value : 0;
      hash = 4096 * hash + addum;
      hash %= p;
    });
    return hash;
  }

  findMemoization(grid) {
    return this.memo[this.getHashCode(grid)];
  }

  addMemoization(grid, move, score) {
    const hash = this.getHashCode(grid);
    this.memo[hash] = { move: move, score: score };
  }

  // Expectimax深度优先搜索
  getMoveExpectimaxDFS(grid, depth) {
    if (depth === 0) {
      const result = { move: -1, score: this.getHeuristic(grid) };
      this.addMemoization(grid, result.move, result.score);
      return result;
    }

    // 检查记忆化缓存
    const memoized = this.findMemoization(grid);
    if (memoized) {
      return memoized;
    }

    const candidates = [];
    
    for (let direction = 0; direction < 4; direction++) {
      const newGrid = grid.clone();
      const newScore = newGrid.move(direction);
      
      if (newScore === -1) {
        candidates[direction] = -999999;
      } else {
        const availableCells = newGrid.availableCells();
        const nEmptyCells = availableCells.length;
        let expectedScore = 0;
        
        for (const cell of availableCells) {
          // 添加2的情况 (90%概率)
          const newGrid2 = newGrid.clone();
          newGrid2.insertTile({ position: cell, value: 2 });
          expectedScore += 0.9 * (1.0 / nEmptyCells) * this.getMoveExpectimaxDFS(newGrid2, depth - 1).score;
          
          // 添加4的情况 (10%概率)
          const newGrid4 = newGrid.clone();
          newGrid4.insertTile({ position: cell, value: 4 });
          expectedScore += 0.1 * (1.0 / nEmptyCells) * this.getMoveExpectimaxDFS(newGrid4, depth - 1).score;
        }
        
        candidates[direction] = expectedScore;
      }
    }

    // 选择最佳移动
    let bestMove = { move: 0, score: candidates[0] };
    for (let direction = 1; direction < 4; direction++) {
      if (candidates[direction] > bestMove.score) {
        bestMove.score = candidates[direction];
        bestMove.move = direction;
      }
    }

    // 添加到记忆化缓存
    this.addMemoization(grid, bestMove.move, bestMove.score);
    return bestMove;
  }

  // 启发式评估函数 - 优化版本
  getHeuristic(grid) {
    let score = 0;
    const largest = grid.largest();

    // 评估每个格子
    grid.eachCell((x, y, cell) => {
      if (!cell) {
        // 空格子获得高分
        score += 4096;
      } else {
        // 惩罚中间位置的大数字
        const distance = Math.min(Math.min(x, 3 - x), Math.min(y, 3 - y));
        score -= 10 * distance * cell.value;

        // 最大值在边角的奖励
        if (cell.value === largest) {
          const xBorder = (x === 0 || x === 3);
          const yBorder = (y === 0 || y === 3);
          if (xBorder && yBorder) {
            score += 8192; // 角落奖励
          } else if (xBorder || yBorder) {
            score += 2048; // 边缘奖励
          }
        }
      }
    });

    // 平滑性惩罚 - 相邻格子差异过大的惩罚
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        const current = grid.cells[x][y];
        if (current) {
          // 检查右边的格子
          if (y < 3 && grid.cells[x][y + 1]) {
            score -= 10 * Math.abs(current.value - grid.cells[x][y + 1].value);
          }
          // 检查下面的格子
          if (x < 3 && grid.cells[x + 1][y]) {
            score -= 10 * Math.abs(current.value - grid.cells[x + 1][y].value);
          }
        }
      }
    }

    // 单调性奖励 - 鼓励数字按顺序排列
    score += this.getMonotonicityScore(grid);

    // 合并潜力奖励
    score += this.getMergePotentialScore(grid);

    return score;
  }

  // 单调性评分
  getMonotonicityScore(grid) {
    let score = 0;

    // 检查行的单调性
    for (let x = 0; x < 4; x++) {
      let increasing = 0, decreasing = 0;
      for (let y = 0; y < 3; y++) {
        const current = grid.cells[x][y] ? Math.log2(grid.cells[x][y].value) : 0;
        const next = grid.cells[x][y + 1] ? Math.log2(grid.cells[x][y + 1].value) : 0;

        if (current > next) {
          decreasing += current - next;
        } else if (current < next) {
          increasing += next - current;
        }
      }
      score += Math.max(increasing, decreasing) * 100;
    }

    // 检查列的单调性
    for (let y = 0; y < 4; y++) {
      let increasing = 0, decreasing = 0;
      for (let x = 0; x < 3; x++) {
        const current = grid.cells[x][y] ? Math.log2(grid.cells[x][y].value) : 0;
        const next = grid.cells[x + 1][y] ? Math.log2(grid.cells[x + 1][y].value) : 0;

        if (current > next) {
          decreasing += current - next;
        } else if (current < next) {
          increasing += next - current;
        }
      }
      score += Math.max(increasing, decreasing) * 100;
    }

    return score;
  }

  // 合并潜力评分
  getMergePotentialScore(grid) {
    let score = 0;

    // 检查水平合并潜力
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 3; y++) {
        const current = grid.cells[x][y];
        const next = grid.cells[x][y + 1];
        if (current && next && current.value === next.value) {
          score += current.value * 2;
        }
      }
    }

    // 检查垂直合并潜力
    for (let y = 0; y < 4; y++) {
      for (let x = 0; x < 3; x++) {
        const current = grid.cells[x][y];
        const next = grid.cells[x + 1][y];
        if (current && next && current.value === next.value) {
          score += current.value * 2;
        }
      }
    }

    return score;
  }
}

// Tile类定义
class Tile {
  constructor(position, value) {
    this.position = position;
    this.value = value;
  }
}

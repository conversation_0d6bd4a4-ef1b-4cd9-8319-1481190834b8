// Background Service Worker
chrome.runtime.onInstalled.addListener(() => {
  console.log('2048 AI Player extension installed');
});

// 处理来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'updateStatus') {
    // 可以在这里处理状态更新，比如更新badge
    if (request.type === 'success') {
      chrome.action.setBadgeText({text: 'ON'});
      chrome.action.setBadgeBackgroundColor({color: '#4CAF50'});
    } else if (request.type === 'error') {
      chrome.action.setBadgeText({text: 'ERR'});
      chrome.action.setBadgeBackgroundColor({color: '#F44336'});
    } else {
      chrome.action.setBadgeText({text: ''});
    }
  }
  
  sendResponse({success: true});
});

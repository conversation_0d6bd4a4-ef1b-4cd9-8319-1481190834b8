// 背景脚本 - 处理插件的生命周期和消息传递

chrome.runtime.onInstalled.addListener(() => {
  console.log('2048 Auto Player 插件已安装');
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'updateBadge') {
    // 更新插件图标上的徽章
    chrome.action.setBadgeText({
      text: request.text,
      tabId: sender.tab.id
    });
    chrome.action.setBadgeBackgroundColor({
      color: request.color || '#4CAF50',
      tabId: sender.tab.id
    });
  }
  
  if (request.action === 'logMessage') {
    console.log('2048 Auto Player:', request.message);
  }
});

// 当标签页更新时清除徽章
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    chrome.action.setBadgeText({text: '', tabId: tabId});
  }
});

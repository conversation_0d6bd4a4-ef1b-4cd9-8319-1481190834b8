// 终极版2048 AI - 针对200,000分优化
// 基于多项研究和最佳实践的综合实现

class Ultimate2048AI {
  constructor() {
    this.depthlimit = 6; // 增加搜索深度
    this.memo = new Map();
    
    // 优化的权重系统
    this.cornerWeight = 100000;
    this.monotonicityWeight = 47;
    this.smoothnessWeight = 11;
    this.emptyWeight = 270;
    this.maxWeight = 10;
    
    // 预计算的位置权重矩阵 - 强化角落策略
    this.positionWeights = [
      [65536, 32768, 16384, 8192],
      [32768, 16384, 8192, 4096],
      [16384, 8192, 4096, 2048],
      [8192, 4096, 2048, 1024]
    ];
  }

  // 主要接口
  getBestMove(board) {
    console.log('Ultimate AI: Getting best move for board:', board);
    
    try {
      const startTime = Date.now();
      this.memo.clear();
      
      const result = this.expectimax(board, this.depthlimit, true);
      const move = result.move;
      
      const endTime = Date.now();
      console.log(`Ultimate AI: Move ${move} selected in ${endTime - startTime}ms, score: ${result.score}`);
      
      const directions = ['up', 'right', 'down', 'left'];
      const direction = directions[move] || 'up';
      console.log(`Ultimate AI: Returning direction: ${direction}`);
      return direction;
    } catch (error) {
      console.error('Ultimate AI: Error in getBestMove:', error);
      return 'up';
    }
  }

  // Expectimax算法
  expectimax(board, depth, isPlayerTurn) {
    if (depth === 0) {
      const score = this.evaluateBoard(board);
      return { move: -1, score: score };
    }

    const boardKey = this.getBoardKey(board);
    const cacheKey = `${boardKey}_${depth}_${isPlayerTurn}`;
    if (this.memo.has(cacheKey)) {
      return this.memo.get(cacheKey);
    }

    let result;
    if (isPlayerTurn) {
      result = this.playerTurn(board, depth);
    } else {
      result = this.boardTurn(board, depth);
    }

    this.memo.set(cacheKey, result);
    return result;
  }

  // 玩家回合
  playerTurn(board, depth) {
    let bestScore = -Infinity;
    let bestMove = 0;

    for (let move = 0; move < 4; move++) {
      const newBoard = this.makeMove(board, move);
      
      if (this.boardsEqual(newBoard, board)) {
        continue;
      }

      const result = this.expectimax(newBoard, depth - 1, false);
      
      if (result.score > bestScore) {
        bestScore = result.score;
        bestMove = move;
      }
    }

    return { move: bestMove, score: bestScore };
  }

  // 棋盘回合
  boardTurn(board, depth) {
    const emptyCells = this.getEmptyCells(board);
    
    if (emptyCells.length === 0) {
      return { move: -1, score: this.evaluateBoard(board) };
    }

    let totalScore = 0;
    
    for (const cell of emptyCells) {
      // 90% 概率出现 2
      const board2 = this.copyBoard(board);
      board2[cell.x][cell.y] = 2;
      const result2 = this.expectimax(board2, depth - 1, true);
      totalScore += 0.9 * result2.score;

      // 10% 概率出现 4
      const board4 = this.copyBoard(board);
      board4[cell.x][cell.y] = 4;
      const result4 = this.expectimax(board4, depth - 1, true);
      totalScore += 0.1 * result4.score;
    }

    return { move: -1, score: totalScore / emptyCells.length };
  }

  // 终极评估函数 - 多因子综合评估
  evaluateBoard(board) {
    let score = 0;
    
    // 1. 位置权重评分 - 强化角落策略
    score += this.getPositionScore(board);
    
    // 2. 单调性评分
    score += this.monotonicityWeight * this.getMonotonicityScore(board);
    
    // 3. 平滑性评分
    score += this.smoothnessWeight * this.getSmoothnessScore(board);
    
    // 4. 空格评分
    score += this.emptyWeight * this.getEmptyScore(board);
    
    // 5. 最大值评分
    score += this.maxWeight * this.getMaxScore(board);
    
    // 6. 合并潜力评分
    score += this.getMergePotentialScore(board);
    
    return score;
  }

  // 位置权重评分 - 鼓励大数字在角落
  getPositionScore(board) {
    let score = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] > 0) {
          score += board[x][y] * this.positionWeights[x][y];
        }
      }
    }
    return score;
  }

  // 单调性评分 - 鼓励递减排列
  getMonotonicityScore(board) {
    let totals = [0, 0, 0, 0]; // up, right, down, left
    
    // 检查行的单调性
    for (let x = 0; x < 4; x++) {
      let current = 0;
      let next = current + 1;
      while (next < 4) {
        while (next < 4 && board[x][next] === 0) next++;
        if (next >= 4) next--;
        
        const currentValue = board[x][current] > 0 ? Math.log2(board[x][current]) : 0;
        const nextValue = board[x][next] > 0 ? Math.log2(board[x][next]) : 0;
        
        if (currentValue > nextValue) {
          totals[1] += nextValue - currentValue;
        } else if (nextValue > currentValue) {
          totals[3] += currentValue - nextValue;
        }
        
        current = next;
        next++;
      }
    }
    
    // 检查列的单调性
    for (let y = 0; y < 4; y++) {
      let current = 0;
      let next = current + 1;
      while (next < 4) {
        while (next < 4 && board[next][y] === 0) next++;
        if (next >= 4) next--;
        
        const currentValue = board[current][y] > 0 ? Math.log2(board[current][y]) : 0;
        const nextValue = board[next][y] > 0 ? Math.log2(board[next][y]) : 0;
        
        if (currentValue > nextValue) {
          totals[2] += nextValue - currentValue;
        } else if (nextValue > currentValue) {
          totals[0] += currentValue - nextValue;
        }
        
        current = next;
        next++;
      }
    }
    
    return Math.max(totals[0], totals[1]) + Math.max(totals[2], totals[3]);
  }

  // 平滑性评分 - 惩罚相邻格子差异过大
  getSmoothnessScore(board) {
    let smoothness = 0;
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] > 0) {
          const value = Math.log2(board[x][y]);
          
          // 检查右边
          if (y < 3 && board[x][y + 1] > 0) {
            const targetValue = Math.log2(board[x][y + 1]);
            smoothness -= Math.abs(value - targetValue);
          }
          
          // 检查下面
          if (x < 3 && board[x + 1][y] > 0) {
            const targetValue = Math.log2(board[x + 1][y]);
            smoothness -= Math.abs(value - targetValue);
          }
        }
      }
    }
    
    return smoothness;
  }

  // 空格评分
  getEmptyScore(board) {
    return this.getEmptyCells(board).length;
  }

  // 最大值评分
  getMaxScore(board) {
    let max = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        max = Math.max(max, board[x][y]);
      }
    }
    return max;
  }

  // 合并潜力评分
  getMergePotentialScore(board) {
    let merges = 0;
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] > 0) {
          // 检查右边
          if (y < 3 && board[x][y + 1] === board[x][y]) {
            merges += board[x][y];
          }
          
          // 检查下面
          if (x < 3 && board[x + 1][y] === board[x][y]) {
            merges += board[x][y];
          }
        }
      }
    }
    
    return merges;
  }

  // 执行移动
  makeMove(board, direction) {
    const newBoard = this.copyBoard(board);
    
    switch (direction) {
      case 0: // up
        this.moveUp(newBoard);
        break;
      case 1: // right
        this.moveRight(newBoard);
        break;
      case 2: // down
        this.moveDown(newBoard);
        break;
      case 3: // left
        this.moveLeft(newBoard);
        break;
    }
    
    return newBoard;
  }

  // 移动函数
  moveUp(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[0][y], board[1][y], board[2][y], board[3][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[x][y] = newColumn[x];
      }
    }
  }

  moveRight(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][3], board[x][2], board[x][1], board[x][0]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][3 - y] = newRow[y];
      }
    }
  }

  moveDown(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[3][y], board[2][y], board[1][y], board[0][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[3 - x][y] = newColumn[x];
      }
    }
  }

  moveLeft(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][0], board[x][1], board[x][2], board[x][3]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][y] = newRow[y];
      }
    }
  }

  // 处理一行/列的移动和合并
  processLine(line) {
    // 移除零值
    const filtered = line.filter(val => val !== 0);

    // 合并相同的相邻值
    const merged = [];
    let i = 0;
    while (i < filtered.length) {
      if (i < filtered.length - 1 && filtered[i] === filtered[i + 1]) {
        // 合并
        merged.push(filtered[i] * 2);
        i += 2;
      } else {
        merged.push(filtered[i]);
        i++;
      }
    }

    // 用零填充到长度4
    while (merged.length < 4) {
      merged.push(0);
    }

    return merged;
  }

  // 辅助函数
  getEmptyCells(board) {
    const emptyCells = [];
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === 0) {
          emptyCells.push({ x, y });
        }
      }
    }
    return emptyCells;
  }

  copyBoard(board) {
    return board.map(row => [...row]);
  }

  boardsEqual(board1, board2) {
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board1[x][y] !== board2[x][y]) {
          return false;
        }
      }
    }
    return true;
  }

  getBoardKey(board) {
    return board.map(row => row.join(',')).join('|');
  }
}

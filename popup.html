<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      text-align: center;
      font-weight: bold;
    }
    .status.inactive {
      background-color: #f0f0f0;
      color: #666;
    }
    .status.active {
      background-color: #4CAF50;
      color: white;
    }
    .status.error {
      background-color: #f44336;
      color: white;
    }
    .controls {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }
    button {
      flex: 1;
      padding: 10px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }
    .start-btn {
      background-color: #4CAF50;
      color: white;
    }
    .stop-btn {
      background-color: #f44336;
      color: white;
    }
    .start-btn:disabled, .stop-btn:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    .settings {
      border-top: 1px solid #eee;
      padding-top: 15px;
    }
    .setting-item {
      margin-bottom: 10px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-size: 12px;
    }
    input[type="range"] {
      width: 100%;
    }
    .speed-value {
      text-align: center;
      font-size: 12px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>2048 自动玩家</h2>
  </div>
  
  <div id="status" class="status inactive">
    未检测到游戏
  </div>
  
  <div class="controls">
    <button id="startBtn" class="start-btn" disabled>开始</button>
    <button id="stopBtn" class="stop-btn" disabled>停止</button>
  </div>
  
  <div class="settings">
    <div class="setting-item">
      <label for="speed">移动速度:</label>
      <input type="range" id="speed" min="100" max="2000" value="500" step="100">
      <div class="speed-value" id="speedValue">500ms</div>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>

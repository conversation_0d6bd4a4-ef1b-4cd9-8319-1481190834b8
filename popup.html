<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      text-align: center;
    }
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .controls {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    button {
      padding: 10px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }
    .primary {
      background-color: #007bff;
      color: white;
    }
    .primary:hover {
      background-color: #0056b3;
    }
    .secondary {
      background-color: #6c757d;
      color: white;
    }
    .secondary:hover {
      background-color: #545b62;
    }
    .danger {
      background-color: #dc3545;
      color: white;
    }
    .danger:hover {
      background-color: #c82333;
    }
    .ai-info {
      margin-top: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 5px;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>🤖 2048 AI Player</h3>
  </div>
  
  <div id="status" class="status info">
    准备就绪
  </div>
  
  <div class="controls">
    <button id="startBtn" class="primary">开始自动游戏</button>
    <button id="stopBtn" class="secondary" disabled>停止游戏</button>
    <button id="downloadAI" class="primary">下载AI模块</button>
  </div>
  
  <div class="ai-info">
    <strong>AI模块状态:</strong>
    <div id="aiStatus">未加载</div>
    <div style="margin-top: 5px;">
      <strong>当前策略:</strong> <span id="currentStrategy">网络AI</span>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>

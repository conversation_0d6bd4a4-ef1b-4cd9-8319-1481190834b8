// 经过验证的2048 AI - 基于成功案例的简化实现
// 专注于核心策略：角落+单调性+平滑性

class Proven2048AI {
  constructor() {
    this.searchDepth = 4; // 适中的搜索深度，保证性能
  }

  getBestMove(board) {
    console.log('Proven AI: Analyzing board...');
    
    try {
      const startTime = Date.now();
      
      // 尝试所有四个方向，选择最佳的
      let bestMove = 0;
      let bestScore = -Infinity;
      
      for (let move = 0; move < 4; move++) {
        const newBoard = this.makeMove(board, move);
        
        // 如果移动无效，跳过
        if (this.boardsEqual(board, newBoard)) {
          continue;
        }
        
        // 评估这个移动的质量
        const score = this.evaluateMove(newBoard, this.searchDepth);
        
        if (score > bestScore) {
          bestScore = score;
          bestMove = move;
        }
      }
      
      const endTime = Date.now();
      console.log(`Proven AI: Selected move ${bestMove} with score ${bestScore} in ${endTime - startTime}ms`);
      
      const directions = ['up', 'right', 'down', 'left'];
      return directions[bestMove] || 'up';
    } catch (error) {
      console.error('Proven AI: Error:', error);
      return 'up';
    }
  }

  // 评估移动质量 - 使用简化的expectimax
  evaluateMove(board, depth) {
    if (depth === 0) {
      return this.evaluateBoard(board);
    }
    
    const emptyCells = this.getEmptyCells(board);
    if (emptyCells.length === 0) {
      return this.evaluateBoard(board);
    }
    
    let totalScore = 0;
    let scenarios = 0;
    
    // 只考虑几个关键位置，提高性能
    const cellsToTest = emptyCells.slice(0, Math.min(4, emptyCells.length));
    
    for (const cell of cellsToTest) {
      // 测试放置2
      const board2 = this.copyBoard(board);
      board2[cell.x][cell.y] = 2;
      const score2 = this.getBestMoveScore(board2, depth - 1);
      totalScore += 0.9 * score2;
      scenarios++;
      
      // 测试放置4
      const board4 = this.copyBoard(board);
      board4[cell.x][cell.y] = 4;
      const score4 = this.getBestMoveScore(board4, depth - 1);
      totalScore += 0.1 * score4;
      scenarios++;
    }
    
    return scenarios > 0 ? totalScore / scenarios : this.evaluateBoard(board);
  }

  // 获取最佳移动的分数
  getBestMoveScore(board, depth) {
    let bestScore = -Infinity;
    
    for (let move = 0; move < 4; move++) {
      const newBoard = this.makeMove(board, move);
      
      if (!this.boardsEqual(board, newBoard)) {
        const score = this.evaluateMove(newBoard, depth);
        bestScore = Math.max(bestScore, score);
      }
    }
    
    return bestScore === -Infinity ? this.evaluateBoard(board) : bestScore;
  }

  // 核心评估函数 - 专注于关键因素
  evaluateBoard(board) {
    let score = 0;
    
    // 1. 角落策略 - 最重要的因素
    score += this.getCornerScore(board) * 100000;
    
    // 2. 单调性 - 第二重要
    score += this.getMonotonicityScore(board) * 10000;
    
    // 3. 平滑性 - 减少相邻差异
    score += this.getSmoothnessScore(board) * 1000;
    
    // 4. 空格数量 - 保持灵活性
    score += this.getEmptyCells(board).length * 10000;
    
    // 5. 最大值位置奖励
    score += this.getMaxTilePositionScore(board) * 50000;
    
    return score;
  }

  // 角落策略评分
  getCornerScore(board) {
    const corners = [
      board[0][0], // 左上
      board[0][3], // 右上
      board[3][0], // 左下
      board[3][3]  // 右下
    ];
    
    // 找到最大的角落值
    const maxCorner = Math.max(...corners);
    
    // 如果最大值在角落，给予高分
    const maxValue = this.getMaxValue(board);
    if (maxCorner === maxValue && maxValue > 0) {
      return maxValue;
    }
    
    return 0;
  }

  // 单调性评分 - 简化版本
  getMonotonicityScore(board) {
    let score = 0;
    
    // 检查每一行的单调性
    for (let x = 0; x < 4; x++) {
      let increasing = true;
      let decreasing = true;
      
      for (let y = 0; y < 3; y++) {
        if (board[x][y] < board[x][y + 1]) decreasing = false;
        if (board[x][y] > board[x][y + 1]) increasing = false;
      }
      
      if (increasing || decreasing) score += 100;
    }
    
    // 检查每一列的单调性
    for (let y = 0; y < 4; y++) {
      let increasing = true;
      let decreasing = true;
      
      for (let x = 0; x < 3; x++) {
        if (board[x][y] < board[x + 1][y]) decreasing = false;
        if (board[x][y] > board[x + 1][y]) increasing = false;
      }
      
      if (increasing || decreasing) score += 100;
    }
    
    return score;
  }

  // 平滑性评分
  getSmoothnessScore(board) {
    let score = 0;
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] > 0) {
          // 检查右边
          if (y < 3 && board[x][y + 1] > 0) {
            const diff = Math.abs(board[x][y] - board[x][y + 1]);
            score -= diff;
          }
          
          // 检查下面
          if (x < 3 && board[x + 1][y] > 0) {
            const diff = Math.abs(board[x][y] - board[x + 1][y]);
            score -= diff;
          }
        }
      }
    }
    
    return score;
  }

  // 最大值位置评分
  getMaxTilePositionScore(board) {
    const maxValue = this.getMaxValue(board);
    
    // 找到最大值的位置
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === maxValue) {
          // 角落位置得分最高
          if ((x === 0 || x === 3) && (y === 0 || y === 3)) {
            return maxValue * 2;
          }
          // 边缘位置得分中等
          if (x === 0 || x === 3 || y === 0 || y === 3) {
            return maxValue;
          }
          // 中间位置得分最低
          return 0;
        }
      }
    }
    
    return 0;
  }

  // 获取最大值
  getMaxValue(board) {
    let max = 0;
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        max = Math.max(max, board[x][y]);
      }
    }
    return max;
  }

  // 执行移动
  makeMove(board, direction) {
    const newBoard = this.copyBoard(board);
    
    switch (direction) {
      case 0: // up
        this.moveUp(newBoard);
        break;
      case 1: // right
        this.moveRight(newBoard);
        break;
      case 2: // down
        this.moveDown(newBoard);
        break;
      case 3: // left
        this.moveLeft(newBoard);
        break;
    }
    
    return newBoard;
  }

  // 移动函数
  moveUp(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[0][y], board[1][y], board[2][y], board[3][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[x][y] = newColumn[x];
      }
    }
  }

  moveRight(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][3], board[x][2], board[x][1], board[x][0]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][3 - y] = newRow[y];
      }
    }
  }

  moveDown(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[3][y], board[2][y], board[1][y], board[0][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[3 - x][y] = newColumn[x];
      }
    }
  }

  moveLeft(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][0], board[x][1], board[x][2], board[x][3]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][y] = newRow[y];
      }
    }
  }

  // 处理一行/列的移动和合并
  processLine(line) {
    // 移除零值
    const filtered = line.filter(val => val !== 0);
    
    // 合并相同的相邻值
    const merged = [];
    let i = 0;
    while (i < filtered.length) {
      if (i < filtered.length - 1 && filtered[i] === filtered[i + 1]) {
        merged.push(filtered[i] * 2);
        i += 2;
      } else {
        merged.push(filtered[i]);
        i++;
      }
    }
    
    // 用零填充到长度4
    while (merged.length < 4) {
      merged.push(0);
    }
    
    return merged;
  }

  // 辅助函数
  getEmptyCells(board) {
    const emptyCells = [];
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === 0) {
          emptyCells.push({ x, y });
        }
      }
    }
    return emptyCells;
  }

  copyBoard(board) {
    return board.map(row => [...row]);
  }

  boardsEqual(board1, board2) {
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board1[x][y] !== board2[x][y]) {
          return false;
        }
      }
    }
    return true;
  }
}

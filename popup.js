// Popup控制脚本
document.addEventListener('DOMContentLoaded', function() {
  const startBtn = document.getElementById('startBtn');
  const stopBtn = document.getElementById('stopBtn');
  const downloadAI = document.getElementById('downloadAI');
  const status = document.getElementById('status');
  const aiStatus = document.getElementById('aiStatus');
  const currentStrategy = document.getElementById('currentStrategy');

  // 更新状态显示
  function updateStatus(message, type = 'info') {
    status.textContent = message;
    status.className = `status ${type}`;
  }

  // 更新AI状态
  function updateAIStatus(message) {
    aiStatus.textContent = message;
  }

  // 开始游戏
  startBtn.addEventListener('click', async function() {
    try {
      const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
      
      // 检查是否在2048游戏页面
      if (!tab.url.includes('2048')) {
        updateStatus('请先打开2048游戏页面', 'error');
        return;
      }

      // 发送开始消息到content script
      chrome.tabs.sendMessage(tab.id, {action: 'start'}, function(response) {
        if (chrome.runtime.lastError) {
          updateStatus('无法连接到页面，请刷新后重试', 'error');
          return;
        }
        
        if (response && response.success) {
          updateStatus('AI正在自动游戏中...', 'success');
          startBtn.disabled = true;
          stopBtn.disabled = false;
        } else {
          updateStatus('启动失败: ' + (response?.error || '未知错误'), 'error');
        }
      });
    } catch (error) {
      updateStatus('启动失败: ' + error.message, 'error');
    }
  });

  // 停止游戏
  stopBtn.addEventListener('click', async function() {
    try {
      const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
      
      chrome.tabs.sendMessage(tab.id, {action: 'stop'}, function(response) {
        if (response && response.success) {
          updateStatus('已停止自动游戏', 'info');
          startBtn.disabled = false;
          stopBtn.disabled = true;
        }
      });
    } catch (error) {
      updateStatus('停止失败: ' + error.message, 'error');
    }
  });

  // 下载AI模块
  downloadAI.addEventListener('click', async function() {
    try {
      updateStatus('正在下载AI模块...', 'info');
      updateAIStatus('下载中...');
      
      const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
      
      chrome.tabs.sendMessage(tab.id, {action: 'downloadAI'}, function(response) {
        if (chrome.runtime.lastError) {
          updateStatus('无法连接到页面', 'error');
          updateAIStatus('下载失败');
          return;
        }
        
        if (response && response.success) {
          updateStatus('AI模块下载成功', 'success');
          updateAIStatus('已加载 - ' + response.aiType);
          currentStrategy.textContent = response.aiType;
        } else {
          updateStatus('AI模块下载失败: ' + (response?.error || '未知错误'), 'error');
          updateAIStatus('下载失败');
        }
      });
    } catch (error) {
      updateStatus('下载失败: ' + error.message, 'error');
      updateAIStatus('下载失败');
    }
  });

  // 初始化时检查状态
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    if (tabs[0]) {
      console.log('Current tab URL:', tabs[0].url);

      if (tabs[0].url.includes('2048.linux.do') || tabs[0].url.includes('2048')) {
        updateStatus('检测到2048游戏页面', 'info');

        // 尝试连接到content script
        chrome.tabs.sendMessage(tabs[0].id, {action: 'getStatus'}, function(response) {
          if (chrome.runtime.lastError) {
            console.log('Content script not ready:', chrome.runtime.lastError.message);
            updateStatus('页面加载中，请稍后重试', 'info');
            startBtn.disabled = true;

            // 5秒后重试
            setTimeout(() => {
              chrome.tabs.sendMessage(tabs[0].id, {action: 'getStatus'}, function(response) {
                if (response) {
                  updateStatus('连接成功', 'success');
                  startBtn.disabled = false;

                  if (response.isRunning) {
                    updateStatus('AI正在运行中...', 'success');
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                  }

                  if (response.aiLoaded) {
                    updateAIStatus('已加载 - ' + response.aiType);
                    currentStrategy.textContent = response.aiType;
                  }
                } else {
                  updateStatus('无法连接到页面，请刷新页面', 'error');
                }
              });
            }, 5000);

          } else if (response) {
            updateStatus('连接成功', 'success');
            startBtn.disabled = false;

            if (response.isRunning) {
              updateStatus('AI正在运行中...', 'success');
              startBtn.disabled = true;
              stopBtn.disabled = false;
            }

            if (response.aiLoaded) {
              updateAIStatus('已加载 - ' + response.aiType);
              currentStrategy.textContent = response.aiType;
            }
          } else {
            updateStatus('页面未响应，请刷新页面', 'error');
            startBtn.disabled = true;
          }
        });
      } else {
        updateStatus('请打开 https://2048.linux.do/ 页面', 'error');
        startBtn.disabled = true;
      }
    }
  });
});

document.addEventListener('DOMContentLoaded', function() {
  const statusDiv = document.getElementById('status');
  const startBtn = document.getElementById('startBtn');
  const stopBtn = document.getElementById('stopBtn');
  const speedSlider = document.getElementById('speed');
  const speedValue = document.getElementById('speedValue');
  
  let isPlaying = false;
  
  // 更新速度显示
  speedSlider.addEventListener('input', function() {
    speedValue.textContent = this.value + 'ms';
  });
  
  // 检查当前页面是否有2048游戏
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    chrome.tabs.sendMessage(tabs[0].id, {action: 'checkGame'}, function(response) {
      if (chrome.runtime.lastError) {
        updateStatus('error', '无法连接到页面');
        return;
      }
      
      if (response && response.gameFound) {
        updateStatus('inactive', '检测到2048游戏');
        startBtn.disabled = false;
      } else {
        updateStatus('error', '未检测到2048游戏');
      }
    });
  });
  
  // 开始自动游戏
  startBtn.addEventListener('click', function() {
    const speed = parseInt(speedSlider.value);
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'startAutoPlay',
        speed: speed
      }, function(response) {
        if (response && response.success) {
          isPlaying = true;
          updateStatus('active', '自动游戏进行中...');
          startBtn.disabled = true;
          stopBtn.disabled = false;
        }
      });
    });
  });
  
  // 停止自动游戏
  stopBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'stopAutoPlay'}, function(response) {
        if (response && response.success) {
          isPlaying = false;
          updateStatus('inactive', '自动游戏已停止');
          startBtn.disabled = false;
          stopBtn.disabled = true;
        }
      });
    });
  });
  
  function updateStatus(type, message) {
    statusDiv.className = `status ${type}`;
    statusDiv.textContent = message;
  }
  
  // 监听来自content script的状态更新
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'updateStatus') {
      updateStatus(request.type, request.message);
      if (request.type === 'error' || request.message.includes('游戏结束')) {
        isPlaying = false;
        startBtn.disabled = false;
        stopBtn.disabled = true;
      }
    }
  });
});

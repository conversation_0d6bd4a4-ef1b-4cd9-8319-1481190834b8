document.addEventListener('DOMContentLoaded', function() {
  const statusDiv = document.getElementById('status');
  const startBtn = document.getElementById('startBtn');
  const stopBtn = document.getElementById('stopBtn');
  const speedSlider = document.getElementById('speed');
  const speedValue = document.getElementById('speedValue');
  
  let isPlaying = false;
  
  // 更新速度显示
  speedSlider.addEventListener('input', function() {
    speedValue.textContent = this.value + 'ms';
  });
  
  // 检查当前页面是否有2048游戏
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    if (!tabs[0]) {
      updateStatus('error', '无法获取当前标签页');
      return;
    }

    // 先尝试注入内容脚本（如果还没有注入的话）
    chrome.scripting.executeScript({
      target: { tabId: tabs[0].id },
      files: ['content.js']
    }).then(() => {
      // 等待一小段时间让脚本初始化
      setTimeout(() => {
        chrome.tabs.sendMessage(tabs[0].id, {action: 'checkGame'}, function(response) {
          if (chrome.runtime.lastError) {
            updateStatus('error', '页面可能不支持或正在加载中，请刷新页面后重试');
            console.log('Runtime error:', chrome.runtime.lastError.message);
            return;
          }

          if (response && response.gameFound) {
            updateStatus('inactive', '检测到2048游戏');
            startBtn.disabled = false;
          } else {
            updateStatus('error', '未检测到2048游戏');
          }
        });
      }, 500);
    }).catch((error) => {
      console.log('Script injection error:', error);
      // 如果注入失败，可能是因为脚本已经存在，直接尝试发送消息
      chrome.tabs.sendMessage(tabs[0].id, {action: 'checkGame'}, function(response) {
        if (chrome.runtime.lastError) {
          updateStatus('error', '请刷新页面后重试');
          return;
        }

        if (response && response.gameFound) {
          updateStatus('inactive', '检测到2048游戏');
          startBtn.disabled = false;
        } else {
          updateStatus('error', '未检测到2048游戏');
        }
      });
    });
  });
  
  // 开始自动游戏
  startBtn.addEventListener('click', function() {
    const speed = parseInt(speedSlider.value);
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'startAutoPlay',
        speed: speed
      }, function(response) {
        if (chrome.runtime.lastError) {
          updateStatus('error', '无法启动自动游戏，请刷新页面后重试');
          return;
        }

        if (response && response.success) {
          isPlaying = true;
          updateStatus('active', '自动游戏进行中...');
          startBtn.disabled = true;
          stopBtn.disabled = false;
        } else {
          updateStatus('error', '启动失败，请确保游戏已加载');
        }
      });
    });
  });

  // 停止自动游戏
  stopBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'stopAutoPlay'}, function(response) {
        if (chrome.runtime.lastError) {
          updateStatus('error', '无法停止自动游戏');
          return;
        }

        if (response && response.success) {
          isPlaying = false;
          updateStatus('inactive', '自动游戏已停止');
          startBtn.disabled = false;
          stopBtn.disabled = true;
        }
      });
    });
  });
  
  function updateStatus(type, message) {
    statusDiv.className = `status ${type}`;
    statusDiv.textContent = message;
  }
  
  // 监听来自content script的状态更新
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'updateStatus') {
      updateStatus(request.type, request.message);
      if (request.type === 'error' || request.message.includes('游戏结束')) {
        isPlaying = false;
        startBtn.disabled = false;
        stopBtn.disabled = true;
      }
    }
  });
});

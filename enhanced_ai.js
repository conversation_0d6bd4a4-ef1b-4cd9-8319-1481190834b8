// 增强版2048 AI - 基于研究论文的高性能实现
// 参考: algomaths.tech 的 Bot 2048 实现

class Enhanced2048AI {
  constructor() {
    this.depthlimit = 5; // 适中的搜索深度
    this.memo = new Map(); // 使用Map提高性能
    
    // 预计算8种单调路径
    this.paths = [
      // 从左上角开始，向右然后向下的蛇形路径
      [[0,0],[0,1],[0,2],[0,3],[1,3],[1,2],[1,1],[1,0],[2,0],[2,1],[2,2],[2,3],[3,3],[3,2],[3,1],[3,0]],
      // 从右上角开始，向左然后向下的蛇形路径
      [[0,3],[0,2],[0,1],[0,0],[1,0],[1,1],[1,2],[1,3],[2,3],[2,2],[2,1],[2,0],[3,0],[3,1],[3,2],[3,3]],
      // 从左下角开始，向右然后向上的蛇形路径
      [[3,0],[3,1],[3,2],[3,3],[2,3],[2,2],[2,1],[2,0],[1,0],[1,1],[1,2],[1,3],[0,3],[0,2],[0,1],[0,0]],
      // 从右下角开始，向左然后向上的蛇形路径
      [[3,3],[3,2],[3,1],[3,0],[2,0],[2,1],[2,2],[2,3],[1,3],[1,2],[1,1],[1,0],[0,0],[0,1],[0,2],[0,3]],
      // 从左上角开始，向下然后向右的蛇形路径
      [[0,0],[1,0],[2,0],[3,0],[3,1],[2,1],[1,1],[0,1],[0,2],[1,2],[2,2],[3,2],[3,3],[2,3],[1,3],[0,3]],
      // 从左下角开始，向上然后向右的蛇形路径
      [[3,0],[2,0],[1,0],[0,0],[0,1],[1,1],[2,1],[3,1],[3,2],[2,2],[1,2],[0,2],[0,3],[1,3],[2,3],[3,3]],
      // 从右上角开始，向下然后向左的蛇形路径
      [[0,3],[1,3],[2,3],[3,3],[3,2],[2,2],[1,2],[0,2],[0,1],[1,1],[2,1],[3,1],[3,0],[2,0],[1,0],[0,0]],
      // 从右下角开始，向上然后向左的蛇形路径
      [[3,3],[2,3],[1,3],[0,3],[0,2],[1,2],[2,2],[3,2],[3,1],[2,1],[1,1],[0,1],[0,0],[1,0],[2,0],[3,0]]
    ];
    
    // 权重系数
    this.baseWeight = 100000;
    this.ratio = 2;
  }

  // 主要接口：获取最佳移动
  getBestMove(board) {
    console.log('Enhanced AI: Getting best move for board:', board);
    
    try {
      const startTime = Date.now();
      
      // 清理记忆化缓存
      this.memo.clear();
      
      const result = this.expectimax(board, this.depthlimit, true);
      const move = result.move;
      
      const endTime = Date.now();
      console.log(`Enhanced AI: Move ${move} selected in ${endTime - startTime}ms, score: ${result.score}`);
      
      // 转换移动方向
      const directions = ['up', 'right', 'down', 'left'];
      const direction = directions[move] || 'up';
      console.log(`Enhanced AI: Returning direction: ${direction}`);
      return direction;
    } catch (error) {
      console.error('Enhanced AI: Error in getBestMove:', error);
      return 'up';
    }
  }

  // Expectimax算法
  expectimax(board, depth, isPlayerTurn) {
    if (depth === 0) {
      const score = this.evaluateBoard(board);
      return { move: -1, score: score };
    }

    // 检查记忆化缓存
    const boardKey = this.getBoardKey(board);
    const cacheKey = `${boardKey}_${depth}_${isPlayerTurn}`;
    if (this.memo.has(cacheKey)) {
      return this.memo.get(cacheKey);
    }

    let result;
    if (isPlayerTurn) {
      result = this.playerTurn(board, depth);
    } else {
      result = this.boardTurn(board, depth);
    }

    // 添加到记忆化缓存
    this.memo.set(cacheKey, result);
    return result;
  }

  // 玩家回合 - 选择最佳移动
  playerTurn(board, depth) {
    let bestScore = -Infinity;
    let bestMove = 0;

    for (let move = 0; move < 4; move++) {
      const newBoard = this.makeMove(board, move);
      
      // 如果移动无效，跳过
      if (this.boardsEqual(newBoard, board)) {
        continue;
      }

      const result = this.expectimax(newBoard, depth - 1, false);
      
      if (result.score > bestScore) {
        bestScore = result.score;
        bestMove = move;
      }
    }

    return { move: bestMove, score: bestScore };
  }

  // 棋盘回合 - 计算期望值
  boardTurn(board, depth) {
    const emptyCells = this.getEmptyCells(board);
    
    if (emptyCells.length === 0) {
      return { move: -1, score: this.evaluateBoard(board) };
    }

    let totalScore = 0;
    
    for (const cell of emptyCells) {
      // 90% 概率出现 2
      const board2 = this.copyBoard(board);
      board2[cell.x][cell.y] = 2;
      const result2 = this.expectimax(board2, depth - 1, true);
      totalScore += 0.9 * result2.score;

      // 10% 概率出现 4
      const board4 = this.copyBoard(board);
      board4[cell.x][cell.y] = 4;
      const result4 = this.expectimax(board4, depth - 1, true);
      totalScore += 0.1 * result4.score;
    }

    const averageScore = totalScore / emptyCells.length;
    return { move: -1, score: averageScore };
  }

  // 增强的评估函数 - 基于研究论文
  evaluateBoard(board) {
    let maxScore = -Infinity;

    // 评估所有8种路径，选择最佳的
    for (const path of this.paths) {
      let score = 0;
      let weight = this.baseWeight;

      for (const [x, y] of path) {
        const value = board[x][y];
        score += value * weight;
        weight = Math.floor(weight / this.ratio);
      }

      maxScore = Math.max(maxScore, score);
    }

    // 添加额外的启发式因子
    maxScore += this.getEmptyTileBonus(board);
    maxScore += this.getSmoothnessPenalty(board);
    maxScore += this.getMergeBonus(board);

    return maxScore;
  }

  // 空格奖励
  getEmptyTileBonus(board) {
    const emptyCells = this.getEmptyCells(board);
    return emptyCells.length * 10000; // 每个空格10000分奖励
  }

  // 平滑性惩罚
  getSmoothnessPenalty(board) {
    let penalty = 0;
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        const current = board[x][y];
        if (current === 0) continue;

        // 检查右边
        if (y < 3 && board[x][y + 1] !== 0) {
          penalty -= Math.abs(current - board[x][y + 1]) * 10;
        }
        
        // 检查下面
        if (x < 3 && board[x + 1][y] !== 0) {
          penalty -= Math.abs(current - board[x + 1][y]) * 10;
        }
      }
    }
    
    return penalty;
  }

  // 合并奖励
  getMergeBonus(board) {
    let bonus = 0;
    
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        const current = board[x][y];
        if (current === 0) continue;

        // 检查右边是否可以合并
        if (y < 3 && board[x][y + 1] === current) {
          bonus += current * 2;
        }
        
        // 检查下面是否可以合并
        if (x < 3 && board[x + 1][y] === current) {
          bonus += current * 2;
        }
      }
    }
    
    return bonus;
  }

  // 执行移动
  makeMove(board, direction) {
    const newBoard = this.copyBoard(board);
    
    switch (direction) {
      case 0: // up
        this.moveUp(newBoard);
        break;
      case 1: // right
        this.moveRight(newBoard);
        break;
      case 2: // down
        this.moveDown(newBoard);
        break;
      case 3: // left
        this.moveLeft(newBoard);
        break;
    }
    
    return newBoard;
  }

  // 向上移动
  moveUp(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[0][y], board[1][y], board[2][y], board[3][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[x][y] = newColumn[x];
      }
    }
  }

  // 向右移动
  moveRight(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][3], board[x][2], board[x][1], board[x][0]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][3 - y] = newRow[y];
      }
    }
  }

  // 向下移动
  moveDown(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[3][y], board[2][y], board[1][y], board[0][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[3 - x][y] = newColumn[x];
      }
    }
  }

  // 向左移动
  moveLeft(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][0], board[x][1], board[x][2], board[x][3]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][y] = newRow[y];
      }
    }
  }

  // 处理一行/列的移动和合并
  processLine(line) {
    // 移除零值
    const filtered = line.filter(val => val !== 0);

    // 合并相同的相邻值
    const merged = [];
    let i = 0;
    while (i < filtered.length) {
      if (i < filtered.length - 1 && filtered[i] === filtered[i + 1]) {
        // 合并
        merged.push(filtered[i] * 2);
        i += 2;
      } else {
        merged.push(filtered[i]);
        i++;
      }
    }

    // 用零填充到长度4
    while (merged.length < 4) {
      merged.push(0);
    }

    return merged;
  }

  // 获取空格子
  getEmptyCells(board) {
    const emptyCells = [];
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board[x][y] === 0) {
          emptyCells.push({ x, y });
        }
      }
    }
    return emptyCells;
  }

  // 复制棋盘
  copyBoard(board) {
    return board.map(row => [...row]);
  }

  // 比较两个棋盘是否相等
  boardsEqual(board1, board2) {
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board1[x][y] !== board2[x][y]) {
          return false;
        }
      }
    }
    return true;
  }

  // 生成棋盘的唯一键值
  getBoardKey(board) {
    return board.map(row => row.join(',')).join('|');
  }
}

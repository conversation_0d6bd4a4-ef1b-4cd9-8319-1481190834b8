<!DOCTYPE html>
<html>
<head>
    <title>创建插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px;
            border-radius: 4px;
        }
        .download-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>2048 Auto Player 图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击下面的按钮生成图标</li>
                <li>右键点击生成的图标，选择"图片另存为"</li>
                <li>将图标保存到插件的 icons 文件夹中</li>
                <li>保存文件名分别为：icon16.png, icon48.png, icon128.png</li>
                <li>重新加载插件</li>
            </ol>
        </div>
        
        <div class="download-section">
            <h3>生成的图标：</h3>
            <div>
                <canvas id="icon16" width="16" height="16"></canvas>
                <span>16x16 (工具栏图标)</span>
                <button onclick="downloadIcon('icon16', 'icon16.png')">下载</button>
            </div>
            <div>
                <canvas id="icon48" width="48" height="48"></canvas>
                <span>48x48 (扩展管理页面)</span>
                <button onclick="downloadIcon('icon48', 'icon48.png')">下载</button>
            </div>
            <div>
                <canvas id="icon128" width="128" height="128"></canvas>
                <span>128x128 (Chrome 网上应用店)</span>
                <button onclick="downloadIcon('icon128', 'icon128.png')">下载</button>
            </div>
        </div>
        
        <button onclick="generateAllIcons()" style="background-color: #2196F3; font-size: 16px; padding: 15px 30px;">
            生成所有图标
        </button>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128; // 基于128px设计，然后缩放
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景
            ctx.fillStyle = '#faf8ef';
            ctx.fillRect(0, 0, size, size);
            
            // 游戏容器背景
            const containerSize = size * 0.8;
            const containerOffset = (size - containerSize) / 2;
            ctx.fillStyle = '#bbada0';
            ctx.fillRect(containerOffset, containerOffset, containerSize, containerSize);
            
            // 网格
            const cellSize = containerSize / 5;
            const gridOffset = containerOffset + cellSize * 0.2;
            ctx.fillStyle = '#cdc1b4';
            
            for (let row = 0; row < 4; row++) {
                for (let col = 0; col < 4; col++) {
                    const x = gridOffset + col * cellSize * 0.8;
                    const y = gridOffset + row * cellSize * 0.8;
                    const cellWidth = cellSize * 0.6;
                    ctx.fillRect(x, y, cellWidth, cellWidth);
                }
            }
            
            // 示例瓦片
            if (size >= 48) {
                // 2 瓦片
                ctx.fillStyle = '#eee4da';
                ctx.fillRect(gridOffset, gridOffset, cellSize * 0.6, cellSize * 0.6);
                ctx.fillStyle = '#776e65';
                ctx.font = `${cellSize * 0.3}px Arial`;
                ctx.textAlign = 'center';
                ctx.fillText('2', gridOffset + cellSize * 0.3, gridOffset + cellSize * 0.4);
                
                // 4 瓦片
                ctx.fillStyle = '#ede0c8';
                ctx.fillRect(gridOffset + cellSize * 0.8, gridOffset + cellSize * 0.8, cellSize * 0.6, cellSize * 0.6);
                ctx.fillStyle = '#776e65';
                ctx.fillText('4', gridOffset + cellSize * 1.1, gridOffset + cellSize * 1.2);
            }
            
            // AI 标识
            if (size >= 32) {
                const aiSize = size * 0.2;
                ctx.fillStyle = '#4CAF50';
                ctx.beginPath();
                ctx.arc(size - aiSize, aiSize, aiSize * 0.4, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = `${aiSize * 0.4}px Arial`;
                ctx.textAlign = 'center';
                ctx.fillText('AI', size - aiSize, aiSize + aiSize * 0.1);
            }
        }
        
        function generateAllIcons() {
            const sizes = [16, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                drawIcon(canvas, size);
            });
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 页面加载时自动生成图标
        window.onload = function() {
            generateAllIcons();
        };
    </script>
</body>
</html>

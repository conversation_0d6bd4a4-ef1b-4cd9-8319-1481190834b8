# 2048 自动玩家 Chrome 插件

一个智能的 Chrome 插件，可以自动玩网页上的 2048 游戏。使用先进的 Minimax 算法和启发式评估来实现高效的游戏策略。

## 功能特点

- 🤖 **智能 AI 算法**: 使用 Minimax 算法配合 Alpha-Beta 剪枝
- 🎯 **自动检测游戏**: 自动识别网页上的 2048 游戏
- ⚡ **可调节速度**: 支持调整自动游戏的移动速度
- 🎮 **简单易用**: 一键启动/停止自动游戏
- 🌐 **广泛兼容**: 支持大多数在线 2048 游戏网站

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 下载或克隆此项目到本地
2. 打开 Chrome 浏览器，进入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 插件安装完成！

### 方法二：打包安装

1. 在 `chrome://extensions/` 页面点击"打包扩展程序"
2. 选择项目文件夹，生成 .crx 文件
3. 将 .crx 文件拖拽到 Chrome 扩展页面进行安装

## 使用方法

1. **访问 2048 游戏网站**
   - 打开任意 2048 游戏网站（如 https://play2048.co/）
   
2. **启动插件**
   - 点击浏览器工具栏中的插件图标
   - 如果检测到游戏，会显示"检测到2048游戏"
   
3. **开始自动游戏**
   - 调整移动速度（100ms - 2000ms）
   - 点击"开始"按钮
   - 插件将自动开始玩游戏
   
4. **停止游戏**
   - 点击"停止"按钮即可停止自动游戏

## AI 算法说明

### 核心算法
- **Minimax 算法**: 通过模拟未来几步来选择最优移动
- **Alpha-Beta 剪枝**: 优化搜索效率，减少计算时间
- **启发式评估**: 综合考虑多个因素来评估棋盘状态

### 评估因素
1. **空格数量**: 更多空格意味着更多可能性
2. **单调性**: 数字按顺序排列的程度
3. **平滑性**: 相邻数字的相似程度
4. **角落奖励**: 最大数字在角落的奖励
5. **总分**: 当前棋盘的总分值

## 兼容性

### 支持的网站
- play2048.co
- 2048game.com
- gabrielecirulli.github.io/2048
- 大多数基于原版 2048 的网站

### 浏览器要求
- Chrome 88+
- 支持 Manifest V3 的 Chromium 内核浏览器

## 文件结构

```
2048-auto-player/
├── manifest.json          # 插件配置文件
├── popup.html             # 弹出窗口界面
├── popup.js               # 弹出窗口逻辑
├── content.js             # 内容脚本（游戏检测和AI）
├── background.js          # 后台脚本
├── icons/                 # 插件图标
│   └── icon.svg
└── README.md              # 说明文档
```

## 开发说明

### 主要组件

1. **Game2048Detector**: 游戏检测和操作类
2. **Game2048AI**: AI 算法实现类
3. **Popup Interface**: 用户界面控制

### 自定义配置

可以在 `content.js` 中调整以下参数：
- `maxDepth`: AI 搜索深度（默认 4）
- 评估权重: 调整不同因素的重要性

## 故障排除

### 常见问题

1. **插件无法检测到游戏**
   - 确保游戏已完全加载
   - 刷新页面后重试
   - 检查游戏是否使用标准的 HTML 结构

2. **自动游戏不工作**
   - 检查页面是否有键盘事件监听
   - 尝试手动玩一步后再启动自动模式
   - 确保游戏窗口处于活动状态

3. **移动速度太快/太慢**
   - 在弹出窗口中调整速度滑块
   - 建议速度设置在 300-800ms 之间

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基本的游戏检测和自动游戏功能
- 支持 Minimax AI 算法

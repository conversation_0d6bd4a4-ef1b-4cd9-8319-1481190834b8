// WebAssembly高性能2048 AI包装器
// 基于qpwoeirut/2048-solver项目，声称能达到16384瓦片，成功率34.6%

class WasmAI {
  constructor() {
    this.module = null;
    this.initialized = false;
    this.initPromise = this.initializeWasm();
  }

  async initializeWasm() {
    try {
      console.log('WasmAI: Initializing WebAssembly module...');
      
      // 加载WASM模块
      const wasmResponse = await fetch(chrome.runtime.getURL('players.wasm'));
      const wasmBytes = await wasmResponse.arrayBuffer();
      
      // 创建Module配置
      const moduleConfig = {
        wasmBinary: wasmBytes,
        onRuntimeInitialized: () => {
          console.log('WasmAI: WebAssembly runtime initialized successfully');
          this.initialized = true;
        }
      };
      
      // 加载并初始化模块
      this.module = await new Promise((resolve, reject) => {
        moduleConfig.onRuntimeInitialized = () => {
          console.log('WasmAI: Module ready');
          this.initialized = true;
          resolve(window.Module);
        };
        
        moduleConfig.onAbort = (error) => {
          console.error('WasmAI: Module initialization failed:', error);
          reject(error);
        };
        
        // 设置全局Module对象
        window.Module = moduleConfig;
        
        // 动态加载players.js
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('players.js');
        script.onload = () => {
          console.log('WasmAI: players.js loaded');
        };
        script.onerror = (error) => {
          console.error('WasmAI: Failed to load players.js:', error);
          reject(error);
        };
        document.head.appendChild(script);
      });
      
      console.log('WasmAI: Initialization complete');
      return true;
    } catch (error) {
      console.error('WasmAI: Initialization failed:', error);
      this.initialized = false;
      return false;
    }
  }

  async getBestMove(board) {
    console.log('WasmAI: Getting best move...');
    
    try {
      // 等待初始化完成
      if (!this.initialized) {
        console.log('WasmAI: Waiting for initialization...');
        await this.initPromise;
      }
      
      if (!this.initialized || !this.module) {
        console.error('WasmAI: Module not initialized, falling back to simple strategy');
        return this.getFallbackMove(board);
      }
      
      const startTime = Date.now();
      
      // 转换棋盘格式为WASM期望的格式
      const wasmBoard = this.convertBoardToWasm(board);
      
      // 调用WASM AI函数
      let move;
      try {
        // 尝试调用不同的可能函数名
        if (this.module.getBestMove) {
          move = this.module.getBestMove(wasmBoard);
        } else if (this.module.expectimax_move) {
          move = this.module.expectimax_move(wasmBoard);
        } else if (this.module.get_move) {
          move = this.module.get_move(wasmBoard);
        } else {
          console.error('WasmAI: No suitable AI function found in WASM module');
          return this.getFallbackMove(board);
        }
      } catch (wasmError) {
        console.error('WasmAI: WASM function call failed:', wasmError);
        return this.getFallbackMove(board);
      }
      
      const endTime = Date.now();
      console.log(`WasmAI: Move ${move} calculated in ${endTime - startTime}ms`);
      
      // 转换移动方向
      const directions = ['up', 'right', 'down', 'left'];
      return directions[move] || this.getFallbackMove(board);
      
    } catch (error) {
      console.error('WasmAI: Error in getBestMove:', error);
      return this.getFallbackMove(board);
    }
  }

  convertBoardToWasm(board) {
    // 将4x4棋盘转换为WASM期望的格式
    // 通常是一个16元素的数组或特定的数据结构
    const flatBoard = [];
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        flatBoard.push(board[x][y] || 0);
      }
    }
    return flatBoard;
  }

  getFallbackMove(board) {
    // 简单的回退策略：优先右下角
    console.log('WasmAI: Using fallback strategy');
    
    const moves = ['right', 'down', 'left', 'up'];
    
    for (const move of moves) {
      const newBoard = this.simulateMove(board, move);
      if (!this.boardsEqual(board, newBoard)) {
        console.log(`WasmAI: Fallback selected: ${move}`);
        return move;
      }
    }
    
    return 'up'; // 最后的默认选择
  }

  simulateMove(board, direction) {
    const newBoard = board.map(row => [...row]);
    
    switch (direction) {
      case 'up':
        this.moveUp(newBoard);
        break;
      case 'right':
        this.moveRight(newBoard);
        break;
      case 'down':
        this.moveDown(newBoard);
        break;
      case 'left':
        this.moveLeft(newBoard);
        break;
    }
    
    return newBoard;
  }

  moveUp(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[0][y], board[1][y], board[2][y], board[3][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[x][y] = newColumn[x];
      }
    }
  }

  moveRight(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][3], board[x][2], board[x][1], board[x][0]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][3 - y] = newRow[y];
      }
    }
  }

  moveDown(board) {
    for (let y = 0; y < 4; y++) {
      const column = [board[3][y], board[2][y], board[1][y], board[0][y]];
      const newColumn = this.processLine(column);
      for (let x = 0; x < 4; x++) {
        board[3 - x][y] = newColumn[x];
      }
    }
  }

  moveLeft(board) {
    for (let x = 0; x < 4; x++) {
      const row = [board[x][0], board[x][1], board[x][2], board[x][3]];
      const newRow = this.processLine(row);
      for (let y = 0; y < 4; y++) {
        board[x][y] = newRow[y];
      }
    }
  }

  processLine(line) {
    const filtered = line.filter(val => val !== 0);
    const merged = [];
    let i = 0;
    while (i < filtered.length) {
      if (i < filtered.length - 1 && filtered[i] === filtered[i + 1]) {
        merged.push(filtered[i] * 2);
        i += 2;
      } else {
        merged.push(filtered[i]);
        i++;
      }
    }
    while (merged.length < 4) {
      merged.push(0);
    }
    return merged;
  }

  boardsEqual(board1, board2) {
    for (let x = 0; x < 4; x++) {
      for (let y = 0; y < 4; y++) {
        if (board1[x][y] !== board2[x][y]) {
          return false;
        }
      }
    }
    return true;
  }
}

// 创建一个同步包装器，因为原有代码期望同步调用
class WasmAISync {
  constructor() {
    this.wasmAI = new WasmAI();
    this.lastMove = 'up';
    this.moveQueue = [];
    this.processing = false;
  }

  getBestMove(board) {
    console.log('WasmAISync: Synchronous getBestMove called');
    
    // 异步获取最佳移动，但立即返回一个合理的移动
    this.queueMoveCalculation(board);
    
    // 返回一个基于简单策略的即时移动
    return this.getImmediateMove(board);
  }

  async queueMoveCalculation(board) {
    if (this.processing) return;
    
    this.processing = true;
    try {
      const bestMove = await this.wasmAI.getBestMove(board);
      this.lastMove = bestMove;
      console.log(`WasmAISync: Calculated best move: ${bestMove}`);
    } catch (error) {
      console.error('WasmAISync: Error calculating move:', error);
    } finally {
      this.processing = false;
    }
  }

  getImmediateMove(board) {
    // 使用简单策略立即返回一个移动
    const moves = ['right', 'down', 'left', 'up'];
    
    for (const move of moves) {
      const newBoard = this.wasmAI.simulateMove(board, move);
      if (!this.wasmAI.boardsEqual(board, newBoard)) {
        return move;
      }
    }
    
    return 'up';
  }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2048 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            background-color: #faf8ef;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
        }
        
        h1 {
            color: #776e65;
            font-size: 48px;
            margin: 20px 0;
        }
        
        .game-container {
            background-color: #bbada0;
            border-radius: 10px;
            padding: 10px;
            position: relative;
            margin: 20px auto;
            width: 280px;
            height: 280px;
        }
        
        .grid-container {
            position: absolute;
            z-index: 1;
        }
        
        .grid-row {
            display: flex;
            margin-bottom: 10px;
        }
        
        .grid-row:last-child {
            margin-bottom: 0;
        }
        
        .grid-cell {
            width: 60px;
            height: 60px;
            background-color: #cdc1b4;
            border-radius: 5px;
            margin-right: 10px;
        }
        
        .grid-cell:last-child {
            margin-right: 0;
        }
        
        .tile-container {
            position: absolute;
            z-index: 2;
        }
        
        .tile {
            position: absolute;
            width: 60px;
            height: 60px;
            background-color: #eee4da;
            border-radius: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
            color: #776e65;
            transition: all 0.15s ease-in-out;
        }
        
        .tile-2 { background-color: #eee4da; color: #776e65; }
        .tile-4 { background-color: #ede0c8; color: #776e65; }
        .tile-8 { background-color: #f2b179; color: #f9f6f2; }
        .tile-16 { background-color: #f59563; color: #f9f6f2; }
        .tile-32 { background-color: #f67c5f; color: #f9f6f2; }
        .tile-64 { background-color: #f65e3b; color: #f9f6f2; }
        .tile-128 { background-color: #edcf72; color: #f9f6f2; font-size: 20px; }
        .tile-256 { background-color: #edcc61; color: #f9f6f2; font-size: 20px; }
        .tile-512 { background-color: #edc850; color: #f9f6f2; font-size: 20px; }
        .tile-1024 { background-color: #edc53f; color: #f9f6f2; font-size: 18px; }
        .tile-2048 { background-color: #edc22e; color: #f9f6f2; font-size: 18px; }
        
        .score-container {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        
        .score-box {
            background-color: #bbada0;
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
        }
        
        .instructions {
            margin-top: 30px;
            color: #776e65;
            line-height: 1.6;
        }
        
        .test-info {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>2048</h1>
        
        <div class="score-container">
            <div class="score-box">
                <div>分数</div>
                <div id="score">0</div>
            </div>
            <div class="score-box">
                <div>最佳</div>
                <div id="best">0</div>
            </div>
        </div>
        
        <div class="game-container">
            <div class="grid-container">
                <div class="grid-row">
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                    <div class="grid-cell"></div>
                </div>
            </div>
            
            <div class="tile-container" id="tile-container">
                <!-- 瓦片将通过 JavaScript 动态生成 -->
            </div>
        </div>
        
        <div class="instructions">
            <p><strong>如何游戏:</strong> 使用方向键移动瓦片。当两个相同数字的瓦片碰撞时，它们会合并成一个！</p>
            <p><strong>测试插件:</strong> 安装 2048 自动玩家插件后，点击浏览器工具栏中的插件图标来启动自动游戏。</p>
        </div>
        
        <div class="test-info">
            <h3>插件测试说明：</h3>
            <ol>
                <li>确保已安装 2048 自动玩家 Chrome 插件</li>
                <li>刷新此页面</li>
                <li>点击浏览器工具栏中的插件图标</li>
                <li>如果检测成功，应该显示"检测到2048游戏"</li>
                <li>调整速度后点击"开始"按钮测试自动游戏功能</li>
            </ol>
        </div>
    </div>
    
    <script>
        // 简单的 2048 游戏实现用于测试插件
        class SimpleGame2048 {
            constructor() {
                this.board = Array(4).fill().map(() => Array(4).fill(0));
                this.score = 0;
                this.tileContainer = document.getElementById('tile-container');
                this.scoreElement = document.getElementById('score');
                
                this.addRandomTile();
                this.addRandomTile();
                this.updateDisplay();
                
                document.addEventListener('keydown', (e) => this.handleKeyPress(e));
            }
            
            addRandomTile() {
                const emptyCells = [];
                for (let row = 0; row < 4; row++) {
                    for (let col = 0; col < 4; col++) {
                        if (this.board[row][col] === 0) {
                            emptyCells.push({row, col});
                        }
                    }
                }
                
                if (emptyCells.length > 0) {
                    const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
                    this.board[randomCell.row][randomCell.col] = Math.random() < 0.9 ? 2 : 4;
                }
            }
            
            handleKeyPress(e) {
                let moved = false;
                
                switch(e.key) {
                    case 'ArrowUp':
                        moved = this.moveUp();
                        break;
                    case 'ArrowDown':
                        moved = this.moveDown();
                        break;
                    case 'ArrowLeft':
                        moved = this.moveLeft();
                        break;
                    case 'ArrowRight':
                        moved = this.moveRight();
                        break;
                    default:
                        return;
                }
                
                if (moved) {
                    this.addRandomTile();
                    this.updateDisplay();
                }
                
                e.preventDefault();
            }
            
            moveLeft() {
                let moved = false;
                for (let row = 0; row < 4; row++) {
                    const line = this.board[row].filter(val => val !== 0);
                    for (let i = 0; i < line.length - 1; i++) {
                        if (line[i] === line[i + 1]) {
                            line[i] *= 2;
                            this.score += line[i];
                            line[i + 1] = 0;
                        }
                    }
                    const newLine = line.filter(val => val !== 0);
                    while (newLine.length < 4) newLine.push(0);
                    
                    for (let col = 0; col < 4; col++) {
                        if (this.board[row][col] !== newLine[col]) {
                            moved = true;
                        }
                        this.board[row][col] = newLine[col];
                    }
                }
                return moved;
            }
            
            moveRight() {
                for (let row = 0; row < 4; row++) {
                    this.board[row].reverse();
                }
                const moved = this.moveLeft();
                for (let row = 0; row < 4; row++) {
                    this.board[row].reverse();
                }
                return moved;
            }
            
            moveUp() {
                this.transpose();
                const moved = this.moveLeft();
                this.transpose();
                return moved;
            }
            
            moveDown() {
                this.transpose();
                const moved = this.moveRight();
                this.transpose();
                return moved;
            }
            
            transpose() {
                const newBoard = Array(4).fill().map(() => Array(4).fill(0));
                for (let i = 0; i < 4; i++) {
                    for (let j = 0; j < 4; j++) {
                        newBoard[j][i] = this.board[i][j];
                    }
                }
                this.board = newBoard;
            }
            
            updateDisplay() {
                this.tileContainer.innerHTML = '';
                this.scoreElement.textContent = this.score;
                
                for (let row = 0; row < 4; row++) {
                    for (let col = 0; col < 4; col++) {
                        const value = this.board[row][col];
                        if (value !== 0) {
                            const tile = document.createElement('div');
                            tile.className = `tile tile-${value}`;
                            tile.textContent = value;
                            tile.style.left = (col * 70) + 'px';
                            tile.style.top = (row * 70) + 'px';
                            this.tileContainer.appendChild(tile);
                        }
                    }
                }
            }
        }
        
        // 启动游戏
        window.addEventListener('load', () => {
            window.game = new SimpleGame2048();
            console.log('Game instance created and attached to window.game');
        });
    </script>
</body>
</html>
